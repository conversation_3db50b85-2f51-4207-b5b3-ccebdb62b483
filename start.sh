#!/bin/bash

# AI数字人口型同步系统启动脚本

echo "========================================"
echo "AI数字人口型同步系统"
echo "========================================"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python 3.7+"
    exit 1
fi

echo "Python已安装"
python3 --version
echo

# 主菜单函数
show_menu() {
    echo "请选择操作:"
    echo "1. 安装依赖包"
    echo "2. 启动图形界面"
    echo "3. 运行命令行版本"
    echo "4. 运行示例程序"
    echo "5. 检查依赖"
    echo "6. 退出"
    echo
}

# 安装依赖
install_deps() {
    echo
    echo "正在安装依赖包..."
    python3 install_dependencies.py
    read -p "按回车键继续..."
}

# 启动图形界面
start_gui() {
    echo
    echo "启动图形界面..."
    python3 quick_start.py
}

# 启动命令行版本
start_cli() {
    echo
    echo "启动命令行版本..."
    echo "使用方法: python3 main.py --video 视频文件 --text \"文案内容\""
    echo "示例: python3 main.py --video example.mp4 --text \"你好，欢迎使用AI数字人系统\""
    echo
    
    read -p "请输入视频文件路径: " video_path
    read -p "请输入文案内容: " text_content
    
    if [ -z "$video_path" ]; then
        echo "错误: 视频文件路径不能为空"
        read -p "按回车键继续..."
        return
    fi
    
    if [ -z "$text_content" ]; then
        echo "错误: 文案内容不能为空"
        read -p "按回车键继续..."
        return
    fi
    
    python3 main.py --video "$video_path" --text "$text_content" --verbose
    read -p "按回车键继续..."
}

# 运行示例
start_example() {
    echo
    echo "运行示例程序..."
    python3 example.py
    read -p "按回车键继续..."
}

# 检查依赖
check_deps() {
    echo
    echo "检查依赖..."
    python3 -c "from utils import check_dependencies; print('依赖检查通过' if check_dependencies() else '依赖检查失败')"
    read -p "按回车键继续..."
}

# 主循环
while true; do
    clear
    echo "========================================"
    echo "AI数字人口型同步系统"
    echo "========================================"
    echo
    
    show_menu
    read -p "请输入选择 (1-6): " choice
    
    case $choice in
        1)
            install_deps
            ;;
        2)
            start_gui
            ;;
        3)
            start_cli
            ;;
        4)
            start_example
            ;;
        5)
            check_deps
            ;;
        6)
            echo "退出程序"
            exit 0
            ;;
        *)
            echo "无效选择，请重新输入"
            read -p "按回车键继续..."
            ;;
    esac
done
