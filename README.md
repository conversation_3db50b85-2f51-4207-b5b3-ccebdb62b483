# AI数字人口型同步系统

这是一个基于Python的AI数字人口型同步系统，能够根据输入的视频、语音和文案生成口型同步的视频。

## 功能特性

- 🎬 **视频处理**: 支持多种视频格式，自动人脸检测和预处理
- 🎵 **音频处理**: 音频增强、降噪、格式转换
- 📝 **文案处理**: 文本预处理和语音合成（TTS）
- 💋 **口型同步**: 基于Wav2Lip的高质量口型同步
- 🔧 **灵活配置**: 支持多种参数配置和质量设置

## 系统要求

- Python 3.7+
- CUDA支持的GPU（推荐，CPU也可运行但速度较慢）
- 至少4GB内存
- 足够的磁盘空间存储临时文件和模型

## 安装步骤

### 1. 克隆项目
```bash
git clone <repository-url>
cd AIRobbet
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 安装额外依赖

#### Windows用户
```bash
# 安装dlib（可能需要Visual Studio Build Tools）
pip install cmake
pip install dlib

# 安装ffmpeg
# 下载ffmpeg并添加到PATH，或使用conda安装
conda install ffmpeg
```

#### Linux用户
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install cmake libopenblas-dev liblapack-dev
sudo apt-get install ffmpeg

# CentOS/RHEL
sudo yum install cmake openblas-devel lapack-devel
sudo yum install ffmpeg
```

### 4. 下载模型
系统首次运行时会自动下载Wav2Lip模型，也可以手动下载：
```bash
mkdir -p models
wget https://github.com/Rudrabha/Wav2Lip/releases/download/Models/wav2lip_gan.pth -O models/wav2lip_gan.pth
```

## 使用方法

### 基本用法

#### 1. 使用视频和文案（推荐）
```bash
python main.py --video ./assets/demo1_video.mp4 --text "你好，欢迎使用AI数字人系统！" --output ./output/result.mp4
```

#### 2. 使用视频、音频和文案
```bash
python main.py --video ./assets/demo1_video.mp4 --audio ./assets/demo1_audio.wav --text "Artificial Intelligence (AI) is transforming the way we live, work, and interact with technology. From smart assistants that help manage our daily tasks to advanced systems that drive innovation in healthcare, finance, and beyond, AI is at the forefront of the digital revolution. By simulating human intelligence—learning, reasoning, and problem-solving—AI empowers businesses to make smarter decisions, enhance efficiency, and unlock new possibilities. As AI continues to evolve, it promises to reshape industries, improve lives, and redefine the future of humanity. Embrace the power of AI and discover a world of endless potential." --output result1.mp4
```

#### 3. 详细输出模式
```bash
python main.py --video input_video.mp4 --text "你好，欢迎使用AI数字人系统！" --verbose
```

### 参数说明

- `--video`: 输入视频文件路径（必需）
- `--audio`: 输入音频文件路径（可选）
- `--text`: 文案内容（必需）
- `--output`: 输出视频文件路径（可选，默认自动生成）
- `--verbose`: 启用详细输出模式

### 支持的文件格式

#### 视频格式
- MP4, AVI, MOV, MKV, WMV

#### 音频格式
- WAV, MP3, AAC, FLAC

## 配置说明

主要配置在 `config.py` 文件中：

```python
# 音频配置
AUDIO_SAMPLE_RATE = 16000  # 音频采样率
TTS_LANGUAGE = "zh"        # TTS语言（中文）

# 视频配置
VIDEO_FPS = 25             # 视频帧率
VIDEO_QUALITY = "high"     # 视频质量

# 口型同步配置
LIP_SYNC_MODEL = "wav2lip" # 口型同步模型
LIP_SYNC_QUALITY = "high"  # 口型同步质量
```

## 项目结构

```
AIRobbet/
├── main.py              # 主程序入口
├── config.py            # 配置文件
├── utils.py             # 工具函数
├── text_processor.py    # 文案处理模块
├── audio_processor.py   # 音频处理模块
├── video_processor.py   # 视频处理模块
├── lip_sync_engine.py   # 口型同步引擎
├── requirements.txt     # 依赖列表
├── README.md           # 说明文档
├── temp/               # 临时文件目录
├── output/             # 输出文件目录
└── models/             # 模型文件目录
```

## 工作流程

1. **输入验证**: 检查视频、音频文件的有效性
2. **视频预处理**: 人脸检测、尺寸调整、格式转换
3. **音频处理**: 音频增强、降噪、格式统一
4. **文案转语音**: 将文案转换为语音（如果需要）
5. **口型同步**: 使用Wav2Lip生成口型同步视频
6. **后处理**: 合并音视频、质量优化
7. **输出**: 生成最终的口型同步视频

## 性能优化建议

1. **使用GPU**: 确保安装CUDA版本的PyTorch以加速处理
2. **视频质量**: 输入视频分辨率不要过高，建议720p或1080p
3. **音频质量**: 使用清晰的音频文件，避免噪声过多
4. **批处理**: 处理多个文件时可以批量处理以提高效率

## 常见问题

### Q: 处理速度很慢怎么办？
A: 
- 确保使用GPU加速
- 降低视频分辨率
- 检查系统资源使用情况

### Q: 口型同步效果不好？
A: 
- 确保输入视频中人脸清晰可见
- 使用高质量的音频文件
- 调整配置中的质量参数

### Q: 安装依赖失败？
A: 
- 确保Python版本兼容
- 使用虚拟环境避免冲突
- 参考官方文档安装特定依赖

## 技术栈

- **深度学习**: PyTorch, Wav2Lip
- **视频处理**: OpenCV, MoviePy
- **音频处理**: librosa, soundfile
- **语音合成**: gTTS, pyttsx3
- **人脸检测**: face_recognition, dlib

## 许可证

本项目基于MIT许可证开源。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者

---

**注意**: 本系统仅供学习和研究使用，请遵守相关法律法规，不要用于非法用途。
