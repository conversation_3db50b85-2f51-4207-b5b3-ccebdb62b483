@echo off
chcp 65001 >nul
echo ========================================
echo AI数字人口型同步系统
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.7+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python已安装
python --version

echo.
echo 请选择操作:
echo 1. 安装依赖包
echo 2. 启动图形界面
echo 3. 运行命令行版本
echo 4. 运行示例程序
echo 5. 检查依赖
echo 6. 退出
echo.

set /p choice=请输入选择 (1-6): 

if "%choice%"=="1" goto install_deps
if "%choice%"=="2" goto start_gui
if "%choice%"=="3" goto start_cli
if "%choice%"=="4" goto start_example
if "%choice%"=="5" goto check_deps
if "%choice%"=="6" goto exit
goto invalid_choice

:install_deps
echo.
echo 正在安装依赖包...
python install_dependencies.py
pause
goto menu

:start_gui
echo.
echo 启动图形界面...
python quick_start.py
goto menu

:start_cli
echo.
echo 启动命令行版本...
echo 使用方法: python main.py --video 视频文件 --text "文案内容"
echo 示例: python main.py --video example.mp4 --text "你好，欢迎使用AI数字人系统"
echo.
set /p video_path=请输入视频文件路径: 
set /p text_content=请输入文案内容: 

if "%video_path%"=="" (
    echo 错误: 视频文件路径不能为空
    pause
    goto menu
)

if "%text_content%"=="" (
    echo 错误: 文案内容不能为空
    pause
    goto menu
)

python main.py --video "%video_path%" --text "%text_content%" --verbose
pause
goto menu

:start_example
echo.
echo 运行示例程序...
python example.py
pause
goto menu

:check_deps
echo.
echo 检查依赖...
python -c "from utils import check_dependencies; print('依赖检查通过' if check_dependencies() else '依赖检查失败')"
pause
goto menu

:invalid_choice
echo 无效选择，请重新输入
pause
goto menu

:menu
echo.
echo 返回主菜单...
goto start

:exit
echo 退出程序
exit /b 0

:start
cls
goto :eof
