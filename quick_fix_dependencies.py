"""
快速修复依赖问题的脚本
"""
import subprocess
import sys
import os

def install_compatible_packages():
    """安装兼容的包版本"""
    print("🔧 快速修复依赖问题")
    print("=" * 40)
    
    # 兼容的版本组合
    packages = [
        "numpy==1.21.6",
        "opencv-python==********", 
        "librosa==0.8.1",
        "soundfile==0.12.1",
        "scipy==1.7.3",
        "moviepy==1.0.3",
        "pillow==9.5.0",
        "torch==1.13.1",
        "torchvision==0.14.1",
        "torchaudio==0.13.1"
    ]
    
    print("正在安装兼容的包版本...")
    
    # 先卸载可能有问题的包
    problematic_packages = ["numpy", "librosa", "opencv-python"]
    for package in problematic_packages:
        try:
            subprocess.run([sys.executable, "-m", "pip", "uninstall", package, "-y"], 
                          capture_output=True)
            print(f"✅ 卸载 {package}")
        except:
            pass
    
    # 安装兼容版本
    success_count = 0
    for package in packages:
        try:
            print(f"正在安装 {package}...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", package
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ {package}")
                success_count += 1
            else:
                print(f"⚠️ {package} - {result.stderr.strip()}")
        except Exception as e:
            print(f"❌ {package} - {e}")
    
    print(f"\n安装结果: {success_count}/{len(packages)} 成功")
    return success_count >= len(packages) * 0.8  # 80%成功率即可

def test_imports():
    """测试导入"""
    print("\n🧪 测试导入...")
    
    test_modules = [
        ("numpy", "import numpy as np"),
        ("opencv", "import cv2"),
        ("librosa", "import librosa"),
        ("soundfile", "import soundfile"),
        ("moviepy", "from moviepy.editor import VideoFileClip"),
        ("torch", "import torch")
    ]
    
    success_count = 0
    for name, import_cmd in test_modules:
        try:
            exec(import_cmd)
            print(f"✅ {name}")
            success_count += 1
        except ImportError as e:
            print(f"❌ {name} - {e}")
        except Exception as e:
            print(f"⚠️ {name} - {e}")
    
    print(f"\n导入测试: {success_count}/{len(test_modules)} 成功")
    return success_count >= len(test_modules) * 0.8

def create_minimal_test():
    """创建最小测试"""
    test_script = '''
import sys
import os

def minimal_test():
    """最小功能测试"""
    print("=== 最小功能测试 ===")
    
    try:
        # 测试基础导入
        import numpy as np
        print("✅ numpy")
        
        import cv2
        print("✅ opencv")
        
        # 测试核心模块
        from config import Config
        print("✅ config")
        
        from utils import validate_file_exists
        print("✅ utils (基础功能)")
        
        print("\\n🎉 基础功能测试通过！")
        print("\\n下一步测试:")
        print("python main.py --video ./assets/demo1_video.mp4 --text \\"测试\\" --output test.mp4")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    minimal_test()
'''
    
    with open("minimal_test.py", "w", encoding="utf-8") as f:
        f.write(test_script)
    
    print("✅ 最小测试脚本已创建: minimal_test.py")

def provide_alternative_solutions():
    """提供备选方案"""
    print("\n🔄 备选解决方案:")
    print("=" * 40)
    
    print("\n方案1: 使用conda环境 (推荐)")
    print("conda create -n airobbet python=3.8")
    print("conda activate airobbet") 
    print("conda install opencv librosa numpy scipy")
    print("pip install moviepy gTTS pyttsx3")
    
    print("\n方案2: 使用Docker")
    print("# 创建Dockerfile，使用预配置的Python环境")
    
    print("\n方案3: 简化版本")
    print("# 仅使用基础功能，不依赖librosa")
    print("pip install opencv-python numpy moviepy")
    
    print("\n方案4: 在线环境")
    print("# 使用Google Colab或Kaggle等在线环境")

def main():
    """主函数"""
    print("AI数字人系统 - 依赖问题快速修复")
    print("=" * 50)
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version.major < 3 or python_version.minor < 8:
        print("⚠️ 建议使用Python 3.8+以获得更好的兼容性")
    
    # 尝试安装兼容包
    if install_compatible_packages():
        print("\n✅ 包安装完成")
        
        # 测试导入
        if test_imports():
            print("\n🎉 所有测试通过！系统应该可以正常运行")
            
            # 创建测试脚本
            create_minimal_test()
            
            print("\n🚀 现在可以测试系统:")
            print("python minimal_test.py")
            print("python main.py --video ./assets/demo1_video.mp4 --text \"测试\" --output test.mp4")
            
        else:
            print("\n⚠️ 部分导入失败，但基础功能可能可用")
            provide_alternative_solutions()
    else:
        print("\n❌ 包安装失败")
        provide_alternative_solutions()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
