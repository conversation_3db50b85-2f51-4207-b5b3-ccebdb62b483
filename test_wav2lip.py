
import os
import sys

def test_wav2lip_setup():
    print("=== Wav2Lip安装测试 ===")
    
    # 检查文件
    files_to_check = [
        ("Wav2Lip/inference.py", "推理脚本"),
        ("models/wav2lip_gan.pth", "主模型文件"),
        ("Wav2Lip/models/wav2lip.py", "模型代码")
    ]
    
    all_good = True
    for file_path, description in files_to_check:
        if os.path.exists(file_path):
            if file_path.endswith('.pth'):
                size = os.path.getsize(file_path) / (1024*1024)
                print(f"✅ {description} ({size:.1f}MB)")
            else:
                print(f"✅ {description}")
        else:
            print(f"❌ {description} 缺失")
            all_good = False
    
    # 测试导入
    try:
        sys.path.insert(0, 'Wav2Lip')
        import models.wav2lip
        print("✅ Wav2Lip模块导入成功")
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        all_good = False
    
    if all_good:
        print("\n🎉 Wav2Lip安装完成，可以使用完整功能！")
    else:
        print("\n⚠️ Wav2Lip安装不完整，将使用简单口型同步")
    
    return all_good

if __name__ == "__main__":
    test_wav2lip_setup()
