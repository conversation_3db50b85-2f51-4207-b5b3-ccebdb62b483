"""
简单的模型下载脚本
"""
import os
import urllib.request
import sys

def download_with_progress(url, filename):
    """带进度条的下载"""
    def progress_hook(block_num, block_size, total_size):
        downloaded = block_num * block_size
        if total_size > 0:
            percent = min(100, (downloaded / total_size) * 100)
            sys.stdout.write(f'\r下载进度: {percent:.1f}% ({downloaded/1024/1024:.1f}MB/{total_size/1024/1024:.1f}MB)')
            sys.stdout.flush()
    
    try:
        urllib.request.urlretrieve(url, filename, progress_hook)
        print(f'\n✅ 下载完成: {filename}')
        return True
    except Exception as e:
        print(f'\n❌ 下载失败: {e}')
        return False

def main():
    # 创建models目录
    os.makedirs('models', exist_ok=True)
    
    model_file = 'models/wav2lip_gan.pth'
    
    if os.path.exists(model_file):
        size = os.path.getsize(model_file) / (1024*1024)
        print(f'✅ 模型文件已存在 ({size:.1f}MB)')
        return
    
    print('正在下载Wav2Lip模型文件（约300MB）...')
    url = 'https://github.com/Rudrabha/Wav2Lip/releases/download/Models/wav2lip_gan.pth'
    
    if download_with_progress(url, model_file):
        print('🎉 模型下载完成！')
    else:
        print('❌ 下载失败，请手动下载：')
        print('1. 访问: https://github.com/Rudrabha/Wav2Lip/releases')
        print('2. 下载 wav2lip_gan.pth 文件')
        print('3. 放到 models/ 目录下')

if __name__ == '__main__':
    main()
