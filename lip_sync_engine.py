"""
口型同步引擎 - 核心口型同步功能
"""
import os
import sys
import cv2
import numpy as np
import torch
import logging
from typing import Optional, Tuple
import subprocess
import tempfile
from config import Config
from utils import validate_video_file, validate_audio_file

logger = logging.getLogger(__name__)

class LipSyncEngine:
    """口型同步引擎"""
    
    def __init__(self):
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        self.model_loaded = False
        self.wav2lip_model = None
        logger.info(f"使用设备: {self.device}")
    
    def download_wav2lip_model(self) -> bool:
        """下载Wav2Lip模型"""
        model_url = "https://github.com/Rudrabha/Wav2Lip/releases/download/Models/wav2lip_gan.pth"
        model_path = Config.WAV2LIP_CHECKPOINT_PATH
        
        if os.path.exists(model_path):
            logger.info("Wav2Lip模型已存在")
            return True
        
        try:
            import requests
            logger.info("正在下载Wav2Lip模型...")
            
            response = requests.get(model_url, stream=True)
            response.raise_for_status()
            
            os.makedirs(os.path.dirname(model_path), exist_ok=True)
            
            with open(model_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            logger.info(f"Wav2Lip模型下载完成: {model_path}")
            return True
            
        except Exception as e:
            logger.error(f"Wav2Lip模型下载失败: {e}")
            return False
    
    def setup_wav2lip_environment(self) -> bool:
        """设置Wav2Lip环境"""
        try:
            # 检查是否已安装Wav2Lip
            wav2lip_dir = os.path.join(Config.BASE_DIR, "Wav2Lip")
            
            if not os.path.exists(wav2lip_dir):
                logger.info("正在克隆Wav2Lip仓库...")
                subprocess.run([
                    "git", "clone", "https://github.com/Rudrabha/Wav2Lip.git", wav2lip_dir
                ], check=True)
            
            # 添加Wav2Lip到Python路径
            if wav2lip_dir not in sys.path:
                sys.path.append(wav2lip_dir)
            
            # 下载模型
            return self.download_wav2lip_model()
            
        except Exception as e:
            logger.error(f"Wav2Lip环境设置失败: {e}")
            return False
    
    def load_wav2lip_model(self) -> bool:
        """加载Wav2Lip模型"""
        if self.model_loaded:
            return True
        
        try:
            # 设置环境
            if not self.setup_wav2lip_environment():
                return False
            
            # 导入Wav2Lip模块
            from models import Wav2Lip
            
            # 加载模型
            model = Wav2Lip()
            checkpoint = torch.load(Config.WAV2LIP_CHECKPOINT_PATH, map_location=self.device)
            model.load_state_dict(checkpoint['state_dict'])
            model.to(self.device)
            model.eval()
            
            self.wav2lip_model = model
            self.model_loaded = True
            
            logger.info("Wav2Lip模型加载成功")
            return True
            
        except Exception as e:
            logger.error(f"Wav2Lip模型加载失败: {e}")
            return False
    
    def preprocess_video_for_wav2lip(self, video_path: str) -> np.ndarray:
        """为Wav2Lip预处理视频"""
        cap = cv2.VideoCapture(video_path)
        frames = []
        
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # 调整大小到96x96（Wav2Lip要求）
                frame = cv2.resize(frame, (96, 96))
                frames.append(frame)
            
            return np.array(frames)
            
        finally:
            cap.release()
    
    def preprocess_audio_for_wav2lip(self, audio_path: str) -> np.ndarray:
        """为Wav2Lip预处理音频"""
        try:
            import librosa
            
            # 加载音频
            audio, sr = librosa.load(audio_path, sr=16000)
            
            # 转换为mel频谱图
            mel_spec = librosa.feature.melspectrogram(
                y=audio, 
                sr=sr, 
                n_mels=80, 
                hop_length=640, 
                win_length=1600
            )
            
            # 转换为dB
            mel_spec_db = librosa.power_to_db(mel_spec, ref=np.max)
            
            return mel_spec_db.T
            
        except Exception as e:
            logger.error(f"音频预处理失败: {e}")
            raise
    
    def run_wav2lip_inference(self, video_frames: np.ndarray, 
                            audio_features: np.ndarray) -> np.ndarray:
        """运行Wav2Lip推理"""
        if not self.model_loaded:
            raise RuntimeError("Wav2Lip模型未加载")
        
        try:
            with torch.no_grad():
                # 转换为tensor
                video_tensor = torch.FloatTensor(video_frames).to(self.device)
                audio_tensor = torch.FloatTensor(audio_features).to(self.device)
                
                # 运行推理
                output_frames = self.wav2lip_model(video_tensor, audio_tensor)
                
                # 转换回numpy
                output_frames = output_frames.cpu().numpy()
                
                return output_frames
                
        except Exception as e:
            logger.error(f"Wav2Lip推理失败: {e}")
            raise
    
    def save_output_video(self, frames: np.ndarray, output_path: str, 
                         fps: float = 25.0) -> bool:
        """保存输出视频"""
        try:
            height, width = frames[0].shape[:2]
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
            
            for frame in frames:
                out.write(frame)
            
            out.release()
            logger.info(f"输出视频保存成功: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存输出视频失败: {e}")
            return False
    
    def run_wav2lip_command_line(self, video_path: str, audio_path: str,
                               output_path: str) -> bool:
        """使用命令行方式运行Wav2Lip"""
        try:
            # 首先检查Wav2Lip是否安装
            wav2lip_dir = os.path.join(Config.BASE_DIR, "Wav2Lip")
            inference_script = os.path.join(wav2lip_dir, "inference.py")

            if not os.path.exists(inference_script):
                logger.error("Wav2Lip inference.py 不存在")
                logger.error("请运行: python setup_wav2lip.py 来安装Wav2Lip")
                return False

            # 检查模型文件
            if not os.path.exists(Config.WAV2LIP_CHECKPOINT_PATH):
                logger.error(f"Wav2Lip模型文件不存在: {Config.WAV2LIP_CHECKPOINT_PATH}")
                logger.error("请运行: python setup_wav2lip.py 来下载模型文件")
                return False

            # 构建命令
            cmd = [
                sys.executable, inference_script,
                "--checkpoint_path", Config.WAV2LIP_CHECKPOINT_PATH,
                "--face", video_path,
                "--audio", audio_path,
                "--outfile", output_path,
                "--static", "True",  # 使用静态模式，更稳定
                "--fps", str(Config.VIDEO_FPS)
            ]

            # 添加可选参数
            if Config.LIP_SYNC_QUALITY == "high":
                cmd.extend(["--resize_factor", "1"])
            else:
                cmd.extend(["--resize_factor", "2"])  # 降低分辨率提高速度

            logger.info(f"运行Wav2Lip命令: {' '.join(cmd)}")

            # 设置环境变量
            env = os.environ.copy()
            env['PYTHONPATH'] = wav2lip_dir + os.pathsep + env.get('PYTHONPATH', '')

            # 运行命令
            result = subprocess.run(cmd, capture_output=True, text=True,
                                  cwd=wav2lip_dir, env=env, timeout=300)

            if result.returncode == 0:
                logger.info("Wav2Lip处理成功")
                if result.stdout:
                    logger.info(f"输出: {result.stdout}")
                return True
            else:
                logger.error(f"Wav2Lip处理失败: {result.stderr}")
                if result.stdout:
                    logger.info(f"标准输出: {result.stdout}")
                return False

        except subprocess.TimeoutExpired:
            logger.error("Wav2Lip处理超时")
            return False
        except Exception as e:
            logger.error(f"Wav2Lip命令行运行失败: {e}")
            return False
    
    def generate_lip_sync_video(self, video_path: str, audio_path: str,
                              output_path: str, method: str = "command_line") -> bool:
        """生成口型同步视频"""
        # 验证输入文件
        if not validate_video_file(video_path):
            logger.error(f"无效的视频文件: {video_path}")
            return False

        if not validate_audio_file(audio_path):
            logger.error(f"无效的音频文件: {audio_path}")
            return False

        logger.info(f"开始生成口型同步视频...")
        logger.info(f"视频: {video_path}")
        logger.info(f"音频: {audio_path}")
        logger.info(f"输出: {output_path}")

        try:
            if method == "command_line":
                # 使用命令行方式（推荐）
                success = self.run_wav2lip_command_line(video_path, audio_path, output_path)

                # 如果Wav2Lip失败，尝试简单口型同步
                if not success:
                    logger.warning("Wav2Lip失败，尝试使用简单口型同步方法...")
                    success = self.run_simple_lip_sync(video_path, audio_path, output_path)

            elif method == "python_api":
                # 使用Python API方式
                if not self.load_wav2lip_model():
                    logger.warning("Wav2Lip模型加载失败，使用简单口型同步方法...")
                    return self.run_simple_lip_sync(video_path, audio_path, output_path)

                # 预处理
                video_frames = self.preprocess_video_for_wav2lip(video_path)
                audio_features = self.preprocess_audio_for_wav2lip(audio_path)

                # 推理
                output_frames = self.run_wav2lip_inference(video_frames, audio_features)

                # 保存
                success = self.save_output_video(output_frames, output_path)

            elif method == "simple":
                # 直接使用简单口型同步
                success = self.run_simple_lip_sync(video_path, audio_path, output_path)

            else:
                logger.error(f"不支持的方法: {method}")
                return False

            if success and os.path.exists(output_path):
                logger.info(f"口型同步视频生成成功: {output_path}")
                return True
            else:
                logger.error("口型同步视频生成失败")
                return False

        except Exception as e:
            logger.error(f"口型同步处理失败: {e}")
            # 最后尝试简单方法
            logger.info("尝试使用简单口型同步作为备用方案...")
            return self.run_simple_lip_sync(video_path, audio_path, output_path)
    
    def run_simple_lip_sync(self, video_path: str, audio_path: str, output_path: str) -> bool:
        """运行简单口型同步（备用方案）"""
        try:
            from simple_lip_sync import SimpleLipSync

            logger.info("使用简单口型同步方法...")
            lip_sync = SimpleLipSync()
            success = lip_sync.apply_simple_lip_sync(video_path, audio_path, output_path)

            if success:
                logger.info("简单口型同步完成")
            else:
                logger.error("简单口型同步失败")

            return success

        except ImportError as e:
            logger.error(f"简单口型同步模块不可用: {e}")
            return self.run_basic_video_audio_combine(video_path, audio_path, output_path)
        except Exception as e:
            logger.error(f"简单口型同步失败: {e}")
            return self.run_basic_video_audio_combine(video_path, audio_path, output_path)

    def run_basic_video_audio_combine(self, video_path: str, audio_path: str, output_path: str) -> bool:
        """基础的视频音频合并（最后的备用方案）"""
        try:
            logger.info("使用基础视频音频合并...")

            # 使用ffmpeg合并
            import subprocess
            cmd = [
                'ffmpeg', '-y',
                '-i', video_path,
                '-i', audio_path,
                '-c:v', 'copy',
                '-c:a', 'aac',
                '-shortest',
                output_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                logger.info("基础视频音频合并完成")
                return True
            else:
                logger.error(f"ffmpeg合并失败: {result.stderr}")
                # 最后的备用方案：简单复制视频
                import shutil
                shutil.copy2(video_path, output_path)
                logger.info("已复制原视频作为输出")
                return True

        except Exception as e:
            logger.error(f"基础合并失败: {e}")
            try:
                # 最后的备用方案：简单复制视频
                import shutil
                shutil.copy2(video_path, output_path)
                logger.info("已复制原视频作为输出")
                return True
            except Exception as e2:
                logger.error(f"视频复制也失败: {e2}")
                return False

    def cleanup(self):
        """清理资源"""
        if self.wav2lip_model is not None:
            del self.wav2lip_model
            self.wav2lip_model = None
            self.model_loaded = False

        try:
            if torch is not None and torch.cuda.is_available():
                torch.cuda.empty_cache()
        except:
            pass
