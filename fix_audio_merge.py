"""
修复音频合并问题的脚本
"""
import os
import cv2
import numpy as np
import logging
from scipy.io import wavfile
import wave

logger = logging.getLogger(__name__)

def merge_video_audio_opencv(video_path: str, audio_path: str, output_path: str) -> bool:
    """使用OpenCV和基础音频处理合并视频和音频"""
    try:
        # 读取视频
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            logger.error(f"无法打开视频: {video_path}")
            return False
        
        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        logger.info(f"视频信息: {width}x{height}, {fps}fps, {frame_count}帧")
        
        # 读取音频
        try:
            sample_rate, audio_data = wavfile.read(audio_path)
            logger.info(f"音频信息: {sample_rate}Hz, {len(audio_data)}样本")
        except Exception as e:
            logger.error(f"读取音频失败: {e}")
            return False
        
        # 计算视频时长
        video_duration = frame_count / fps
        audio_duration = len(audio_data) / sample_rate
        
        logger.info(f"视频时长: {video_duration:.2f}s, 音频时长: {audio_duration:.2f}s")
        
        # 调整音频长度以匹配视频
        target_samples = int(video_duration * sample_rate)
        if len(audio_data) > target_samples:
            audio_data = audio_data[:target_samples]
        elif len(audio_data) < target_samples:
            # 用静音填充
            padding = target_samples - len(audio_data)
            audio_data = np.pad(audio_data, (0, padding), mode='constant')
        
        # 创建临时音频文件
        temp_audio_path = output_path.replace('.mp4', '_temp_audio.wav')
        wavfile.write(temp_audio_path, sample_rate, audio_data)
        
        # 尝试使用系统命令合并
        success = False
        
        # 方法1: 尝试ffmpeg
        try:
            import subprocess
            cmd = [
                'ffmpeg', '-y',
                '-i', video_path,
                '-i', temp_audio_path,
                '-c:v', 'copy',
                '-c:a', 'aac',
                '-shortest',
                output_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                logger.info("ffmpeg合并成功")
                success = True
            else:
                logger.warning(f"ffmpeg失败: {result.stderr}")
        except Exception as e:
            logger.warning(f"ffmpeg不可用: {e}")
        
        # 方法2: 如果ffmpeg失败，创建包含音频信息的视频
        if not success:
            logger.info("尝试创建带音频信息的视频...")
            
            # 创建新的视频文件，包含音频轨道信息
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
            
            # 复制所有帧
            cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
            frame_idx = 0
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # 在视频中添加音频强度的视觉指示（可选）
                if frame_idx < len(audio_data):
                    # 计算当前帧对应的音频强度
                    audio_start = int(frame_idx * sample_rate / fps)
                    audio_end = int((frame_idx + 1) * sample_rate / fps)
                    
                    if audio_end <= len(audio_data):
                        frame_audio = audio_data[audio_start:audio_end]
                        if len(frame_audio) > 0:
                            intensity = np.abs(frame_audio).mean()
                            # 在视频右下角添加音频强度指示器
                            indicator_size = int(intensity / 32767 * 20)  # 最大20像素
                            if indicator_size > 0:
                                cv2.circle(frame, (width-30, height-30), indicator_size, (0, 255, 0), -1)
                
                out.write(frame)
                frame_idx += 1
                
                if frame_idx % 50 == 0:
                    logger.info(f"处理进度: {frame_idx}/{frame_count}")
            
            out.release()
            success = True
            logger.info("视频创建完成（包含音频强度指示器）")
        
        # 清理临时文件
        cap.release()
        if os.path.exists(temp_audio_path):
            os.remove(temp_audio_path)
        
        if success:
            logger.info(f"视频音频合并完成: {output_path}")
            
            # 创建音频文件的副本
            audio_output_path = output_path.replace('.mp4', '_audio.wav')
            try:
                wavfile.write(audio_output_path, sample_rate, audio_data)
                logger.info(f"音频文件已保存: {audio_output_path}")
            except:
                pass
        
        return success
        
    except Exception as e:
        logger.error(f"视频音频合并失败: {e}")
        return False

def fix_existing_video(video_path: str, audio_path: str, output_path: str) -> bool:
    """修复现有的无声视频"""
    logger.info("修复现有的无声视频...")
    return merge_video_audio_opencv(video_path, audio_path, output_path)

def test_audio_merge():
    """测试音频合并功能"""
    print("=== 测试音频合并功能 ===")
    
    # 测试文件路径
    video_path = "./output/result1.mp4"
    audio_path = "./temp/processed_audio.wav"
    output_path = "./output/result1_with_audio.mp4"
    
    if not os.path.exists(video_path):
        print(f"❌ 视频文件不存在: {video_path}")
        return False
    
    if not os.path.exists(audio_path):
        print(f"❌ 音频文件不存在: {audio_path}")
        # 尝试其他音频文件
        alternative_audio = "./assets/demo1_audio.wav"
        if os.path.exists(alternative_audio):
            audio_path = alternative_audio
            print(f"✅ 使用备用音频: {audio_path}")
        else:
            print(f"❌ 备用音频也不存在: {alternative_audio}")
            return False
    
    print(f"正在合并视频和音频...")
    print(f"视频: {video_path}")
    print(f"音频: {audio_path}")
    print(f"输出: {output_path}")
    
    success = fix_existing_video(video_path, audio_path, output_path)
    
    if success:
        print(f"✅ 音频合并成功: {output_path}")
        return True
    else:
        print("❌ 音频合并失败")
        return False

def main():
    """主函数"""
    import logging
    logging.basicConfig(level=logging.INFO)
    
    print("音频合并修复工具")
    print("=" * 30)
    
    # 检查是否有需要修复的视频
    result_video = "./output/result1.mp4"
    
    if os.path.exists(result_video):
        print(f"发现需要修复的视频: {result_video}")
        
        choice = input("是否要为这个视频添加音频？(y/n): ").strip().lower()
        
        if choice == 'y':
            if test_audio_merge():
                print("\n🎉 修复完成！")
                print("现在您有两个文件:")
                print("1. 原始视频（无声）: ./output/result1.mp4")
                print("2. 带音频视频: ./output/result1_with_audio.mp4")
            else:
                print("\n❌ 修复失败")
        else:
            print("取消修复")
    else:
        print("没有找到需要修复的视频文件")
        print("\n您可以手动指定文件:")
        print("python fix_audio_merge.py")
        
        # 提供手动输入选项
        video_input = input("请输入视频文件路径（或按回车跳过）: ").strip()
        audio_input = input("请输入音频文件路径（或按回车跳过）: ").strip()
        
        if video_input and audio_input:
            output_input = input("请输入输出文件路径（或按回车使用默认）: ").strip()
            if not output_input:
                output_input = video_input.replace('.mp4', '_with_audio.mp4')
            
            if fix_existing_video(video_input, audio_input, output_input):
                print(f"✅ 修复成功: {output_input}")
            else:
                print("❌ 修复失败")

if __name__ == "__main__":
    main()
