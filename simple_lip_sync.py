"""
简化的口型同步实现 - 不依赖Wav2Lip的备用方案
"""
import cv2
import numpy as np
import librosa
import os
from moviepy.editor import VideoFileClip, AudioFileClip
import logging

logger = logging.getLogger(__name__)

class SimpleLipSync:
    """简化的口型同步器"""
    
    def __init__(self):
        self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        self.mouth_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_smile.xml')
    
    def detect_mouth_region(self, frame):
        """检测嘴部区域"""
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # 检测人脸
        faces = self.face_cascade.detectMultiScale(gray, 1.1, 5)
        
        if len(faces) == 0:
            return None
        
        # 取最大的人脸
        face = max(faces, key=lambda x: x[2] * x[3])
        x, y, w, h = face
        
        # 估算嘴部位置（人脸下半部分）
        mouth_y = y + int(h * 0.6)
        mouth_h = int(h * 0.4)
        mouth_x = x + int(w * 0.2)
        mouth_w = int(w * 0.6)
        
        return (mouth_x, mouth_y, mouth_w, mouth_h)
    
    def analyze_audio_intensity(self, audio_path, fps=25):
        """分析音频强度"""
        try:
            # 加载音频
            audio, sr = librosa.load(audio_path, sr=22050)
            
            # 计算每帧的音频强度
            hop_length = sr // fps  # 每帧对应的音频样本数
            
            # 计算RMS能量
            rms = librosa.feature.rms(y=audio, hop_length=hop_length)[0]
            
            # 归一化到0-1范围
            if len(rms) > 0:
                rms = (rms - np.min(rms)) / (np.max(rms) - np.min(rms) + 1e-8)
            
            return rms
            
        except Exception as e:
            logger.error(f"音频分析失败: {e}")
            return np.array([])
    
    def create_mouth_animation(self, mouth_region, intensity):
        """创建简单的嘴部动画"""
        x, y, w, h = mouth_region
        
        # 根据音频强度调整嘴部开合
        mouth_opening = int(intensity * h * 0.3)  # 最大开口30%
        
        # 创建椭圆形嘴部
        mouth_center = (x + w//2, y + h//2)
        mouth_axes = (w//3, max(2, mouth_opening))
        
        return mouth_center, mouth_axes
    
    def apply_simple_lip_sync(self, video_path, audio_path, output_path):
        """应用简单的口型同步"""
        try:
            # 打开视频
            cap = cv2.VideoCapture(video_path)
            fps = cap.get(cv2.CAP_PROP_FPS)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            # 分析音频
            audio_intensities = self.analyze_audio_intensity(audio_path, fps)
            
            # 创建输出视频
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            temp_video_path = output_path.replace('.mp4', '_temp.mp4')
            out = cv2.VideoWriter(temp_video_path, fourcc, fps, (width, height))
            
            frame_idx = 0
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # 检测嘴部区域
                mouth_region = self.detect_mouth_region(frame)
                
                if mouth_region is not None and frame_idx < len(audio_intensities):
                    # 获取当前帧的音频强度
                    intensity = audio_intensities[frame_idx]
                    
                    # 创建简单的嘴部动画效果
                    mouth_center, mouth_axes = self.create_mouth_animation(mouth_region, intensity)
                    
                    # 在嘴部区域添加简单的动画效果（可选）
                    if intensity > 0.3:  # 只在有明显声音时添加效果
                        # 在嘴部区域添加轻微的亮度变化
                        x, y, w, h = mouth_region
                        mouth_roi = frame[y:y+h, x:x+w]
                        
                        # 轻微增加亮度表示说话
                        brightness_factor = 1.0 + intensity * 0.1
                        mouth_roi = cv2.convertScaleAbs(mouth_roi, alpha=brightness_factor, beta=0)
                        frame[y:y+h, x:x+w] = mouth_roi
                
                out.write(frame)
                frame_idx += 1
                
                if frame_idx % 100 == 0:
                    logger.info(f"处理进度: {frame_idx} 帧")
            
            cap.release()
            out.release()
            
            # 合并音频
            logger.info("正在合并音频...")
            video_clip = VideoFileClip(temp_video_path)
            audio_clip = AudioFileClip(audio_path)
            
            # 调整音频长度
            if audio_clip.duration > video_clip.duration:
                audio_clip = audio_clip.subclip(0, video_clip.duration)
            
            final_clip = video_clip.set_audio(audio_clip)
            final_clip.write_videofile(output_path, verbose=False, logger=None)
            
            # 清理临时文件
            video_clip.close()
            audio_clip.close()
            final_clip.close()
            os.remove(temp_video_path)
            
            logger.info(f"简单口型同步完成: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"简单口型同步失败: {e}")
            return False

def test_simple_lip_sync():
    """测试简单口型同步"""
    lip_sync = SimpleLipSync()
    
    # 测试文件路径
    video_path = "./assets/demo1_video.mp4"
    audio_path = "./temp/tts_audio.wav"
    output_path = "./output/simple_result.mp4"
    
    if not os.path.exists(video_path):
        print(f"❌ 视频文件不存在: {video_path}")
        return False
    
    if not os.path.exists(audio_path):
        print(f"❌ 音频文件不存在: {audio_path}")
        return False
    
    print("正在使用简单口型同步方法...")
    success = lip_sync.apply_simple_lip_sync(video_path, audio_path, output_path)
    
    if success:
        print(f"✅ 简单口型同步完成: {output_path}")
    else:
        print("❌ 简单口型同步失败")
    
    return success

if __name__ == "__main__":
    test_simple_lip_sync()
