"""
简化的口型同步实现 - 不依赖Wav2Lip的备用方案
"""
import cv2
import numpy as np
import librosa
import os
from moviepy.editor import VideoFileClip, AudioFileClip, concatenate_audioclips
import logging

logger = logging.getLogger(__name__)

class SimpleLipSync:
    """简化的口型同步器"""
    
    def __init__(self):
        self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        self.mouth_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_smile.xml')
    
    def detect_mouth_region(self, frame):
        """检测嘴部区域"""
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # 检测人脸
        faces = self.face_cascade.detectMultiScale(gray, 1.1, 5)
        
        if len(faces) == 0:
            return None
        
        # 取最大的人脸
        face = max(faces, key=lambda x: x[2] * x[3])
        x, y, w, h = face
        
        # 估算嘴部位置（人脸下半部分）
        mouth_y = y + int(h * 0.6)
        mouth_h = int(h * 0.4)
        mouth_x = x + int(w * 0.2)
        mouth_w = int(w * 0.6)
        
        return (mouth_x, mouth_y, mouth_w, mouth_h)
    
    def analyze_audio_intensity(self, audio_path, fps=25):
        """分析音频强度"""
        try:
            # 加载音频
            audio, sr = librosa.load(audio_path, sr=22050)

            # 计算每帧的音频强度
            hop_length = int(sr // fps)  # 确保是整数

            # 计算RMS能量
            rms = librosa.feature.rms(y=audio, hop_length=hop_length)[0]

            # 归一化到0-1范围
            if len(rms) > 0:
                rms_min = np.min(rms)
                rms_max = np.max(rms)
                if rms_max > rms_min:
                    rms = (rms - rms_min) / (rms_max - rms_min)
                else:
                    rms = np.zeros_like(rms)

            logger.info(f"音频分析完成，生成 {len(rms)} 个强度值")
            return rms

        except Exception as e:
            logger.error(f"音频分析失败: {e}")
            import traceback
            traceback.print_exc()
            return np.array([])
    
    def create_mouth_animation(self, mouth_region, intensity):
        """创建简单的嘴部动画"""
        x, y, w, h = mouth_region
        
        # 根据音频强度调整嘴部开合
        mouth_opening = int(intensity * h * 0.3)  # 最大开口30%
        
        # 创建椭圆形嘴部
        mouth_center = (x + w//2, y + h//2)
        mouth_axes = (w//3, max(2, mouth_opening))
        
        return mouth_center, mouth_axes
    
    def apply_simple_lip_sync(self, video_path, audio_path, output_path):
        """应用简单的口型同步"""
        try:
            # 打开视频
            cap = cv2.VideoCapture(video_path)
            fps = cap.get(cv2.CAP_PROP_FPS)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

            logger.info(f"视频信息: {width}x{height}, {fps}fps, {total_frames}帧")

            # 分析音频
            audio_intensities = self.analyze_audio_intensity(audio_path, fps)

            if len(audio_intensities) == 0:
                logger.warning("音频分析失败，使用默认强度")
                audio_intensities = np.random.random(total_frames) * 0.5

            # 确保音频强度数组长度匹配视频帧数
            if len(audio_intensities) < total_frames:
                # 扩展音频强度数组
                padding = total_frames - len(audio_intensities)
                audio_intensities = np.pad(audio_intensities, (0, padding), mode='edge')
            elif len(audio_intensities) > total_frames:
                # 截断音频强度数组
                audio_intensities = audio_intensities[:total_frames]

            logger.info(f"音频强度数组长度: {len(audio_intensities)}, 视频帧数: {total_frames}")

            # 创建输出视频
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            temp_video_path = output_path.replace('.mp4', '_temp.mp4')
            out = cv2.VideoWriter(temp_video_path, fourcc, fps, (width, height))

            frame_idx = 0

            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                # 检测嘴部区域
                mouth_region = self.detect_mouth_region(frame)

                if mouth_region is not None and frame_idx < len(audio_intensities):
                    # 获取当前帧的音频强度
                    intensity = float(audio_intensities[frame_idx])

                    # 在嘴部区域添加动画效果
                    if intensity > 0.1:  # 降低阈值，更敏感
                        x, y, w, h = mouth_region

                        # 确保区域在图像范围内
                        x = max(0, min(x, width - w))
                        y = max(0, min(y, height - h))
                        w = min(w, width - x)
                        h = min(h, height - y)

                        if w > 0 and h > 0:
                            mouth_roi = frame[y:y+h, x:x+w].copy()

                            # 根据音频强度调整嘴部区域
                            # 方法1: 亮度调整
                            brightness_factor = 1.0 + intensity * 0.15
                            mouth_roi = cv2.convertScaleAbs(mouth_roi, alpha=brightness_factor, beta=0)

                            # 方法2: 轻微的色彩调整（增加红色通道表示说话）
                            if len(mouth_roi.shape) == 3:
                                mouth_roi[:, :, 2] = np.clip(mouth_roi[:, :, 2] * (1.0 + intensity * 0.1), 0, 255)

                            frame[y:y+h, x:x+w] = mouth_roi

                out.write(frame)
                frame_idx += 1

                if frame_idx % 50 == 0:
                    progress = (frame_idx / total_frames) * 100
                    logger.info(f"处理进度: {frame_idx}/{total_frames} ({progress:.1f}%)")

            cap.release()
            out.release()

            # 合并音频
            logger.info("正在合并音频...")
            video_clip = VideoFileClip(temp_video_path)
            audio_clip = AudioFileClip(audio_path)

            # 精确调整音频长度以匹配视频
            video_duration = video_clip.duration
            audio_duration = audio_clip.duration

            logger.info(f"视频时长: {video_duration:.2f}s, 音频时长: {audio_duration:.2f}s")

            if abs(audio_duration - video_duration) > 0.1:  # 如果差异超过0.1秒
                if audio_duration > video_duration:
                    audio_clip = audio_clip.subclip(0, video_duration)
                    logger.info(f"音频截断到 {video_duration:.2f}s")
                else:
                    # 如果音频较短，循环播放或延长
                    if audio_duration > 0:
                        loops_needed = int(video_duration / audio_duration) + 1
                        audio_clips = [audio_clip] * loops_needed
                        extended_audio = concatenate_audioclips(audio_clips)
                        audio_clip = extended_audio.subclip(0, video_duration)
                        logger.info(f"音频延长到 {video_duration:.2f}s")

            final_clip = video_clip.set_audio(audio_clip)
            final_clip.write_videofile(output_path, verbose=False, logger=None)

            # 清理临时文件
            video_clip.close()
            audio_clip.close()
            final_clip.close()

            if os.path.exists(temp_video_path):
                os.remove(temp_video_path)

            logger.info(f"简单口型同步完成: {output_path}")
            return True

        except Exception as e:
            logger.error(f"简单口型同步失败: {e}")
            import traceback
            traceback.print_exc()
            return False

def test_simple_lip_sync():
    """测试简单口型同步"""
    lip_sync = SimpleLipSync()
    
    # 测试文件路径
    video_path = "./assets/demo1_video.mp4"
    audio_path = "./temp/tts_audio.wav"
    output_path = "./output/simple_result.mp4"
    
    if not os.path.exists(video_path):
        print(f"❌ 视频文件不存在: {video_path}")
        return False
    
    if not os.path.exists(audio_path):
        print(f"❌ 音频文件不存在: {audio_path}")
        return False
    
    print("正在使用简单口型同步方法...")
    success = lip_sync.apply_simple_lip_sync(video_path, audio_path, output_path)
    
    if success:
        print(f"✅ 简单口型同步完成: {output_path}")
    else:
        print("❌ 简单口型同步失败")
    
    return success

if __name__ == "__main__":
    test_simple_lip_sync()
