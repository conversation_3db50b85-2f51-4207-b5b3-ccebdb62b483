# AI数字人口型同步系统 - 使用指南

## 快速开始

### 第一步：安装依赖

#### 方法1：使用自动安装脚本（推荐）
```bash
# Windows用户
python install_dependencies.py

# 或者双击运行 start.bat，选择"安装依赖包"
```

#### 方法2：手动安装
```bash
pip install -r requirements.txt
```

### 第二步：验证安装
```bash
python test_system.py
```

### 第三步：开始使用

#### 图形界面（推荐新手）
```bash
python quick_start.py
```

#### 命令行界面
```bash
python main.py --video your_video.mp4 --text "你的文案内容"
```

## 详细使用说明

### 1. 准备输入文件

#### 视频要求
- **格式**: MP4, AVI, MOV, MKV, WMV
- **分辨率**: 建议720p-1080p
- **内容**: 包含清晰可见的人脸
- **时长**: 建议10秒-5分钟

#### 音频要求（可选）
- **格式**: WAV, MP3, AAC, FLAC
- **质量**: 清晰无噪声
- **时长**: 与视频匹配或稍短

#### 文案要求
- **语言**: 支持中文
- **长度**: 建议500字以内
- **内容**: 清晰表达，避免过于复杂的句子

### 2. 使用图形界面

1. **启动程序**
   ```bash
   python quick_start.py
   ```

2. **选择输入文件**
   - 点击"浏览"按钮选择视频文件
   - （可选）选择音频文件
   - 在文本框中输入文案内容

3. **设置输出路径**
   - 点击"浏览"按钮选择保存位置
   - 或留空自动生成文件名

4. **开始处理**
   - 点击"开始处理"按钮
   - 等待处理完成
   - 查看输出文件

### 3. 使用命令行界面

#### 基本语法
```bash
python main.py --video <视频文件> --text "<文案内容>" [选项]
```

#### 参数说明
- `--video`: 输入视频文件路径（必需）
- `--text`: 文案内容（必需）
- `--audio`: 输入音频文件路径（可选）
- `--output`: 输出视频文件路径（可选）
- `--verbose`: 显示详细处理信息

#### 使用示例

**示例1：仅使用视频和文案**
```bash
python main.py --video demo.mp4 --text "你好，我是AI数字人助手。欢迎使用口型同步系统！"
```

**示例2：使用视频、音频和文案**
```bash
python main.py --video demo.mp4 --audio voice.wav --text "这是一个演示视频" --output result.mp4
```

**示例3：详细输出模式**
```bash
python main.py --video demo.mp4 --text "测试文案" --verbose
```

### 4. 批处理使用

#### 创建批处理脚本
```python
from main import AIDigitalHuman

ai_human = AIDigitalHuman()

tasks = [
    {"video": "video1.mp4", "text": "第一个视频的文案"},
    {"video": "video2.mp4", "text": "第二个视频的文案"},
    {"video": "video3.mp4", "text": "第三个视频的文案"}
]

for i, task in enumerate(tasks):
    output_path = f"output_{i+1}.mp4"
    success = ai_human.process_with_text_only(
        task["video"], task["text"], output_path
    )
    print(f"任务{i+1}: {'成功' if success else '失败'}")

ai_human.cleanup()
```

## 配置和优化

### 1. 修改配置

编辑 `config.py` 文件来调整系统参数：

```python
# 音频配置
AUDIO_SAMPLE_RATE = 16000  # 音频采样率
TTS_LANGUAGE = "zh"        # TTS语言

# 视频配置
VIDEO_FPS = 25             # 视频帧率
VIDEO_QUALITY = "high"     # 视频质量

# 口型同步配置
LIP_SYNC_QUALITY = "high"  # 口型同步质量
```

### 2. 性能优化

#### GPU加速
- 安装CUDA版本的PyTorch
- 确保GPU驱动程序最新
- 监控GPU内存使用

#### 内存优化
- 处理大文件时分段处理
- 及时清理临时文件
- 关闭不必要的程序

#### 质量设置
```python
# 高质量（慢速）
VIDEO_QUALITY = "high"
LIP_SYNC_QUALITY = "high"

# 中等质量（平衡）
VIDEO_QUALITY = "medium"
LIP_SYNC_QUALITY = "medium"

# 快速处理（低质量）
VIDEO_QUALITY = "low"
LIP_SYNC_QUALITY = "low"
```

## 常见问题解决

### 1. 安装问题

**问题**: 依赖安装失败
```bash
# 解决方案
pip install --upgrade pip
pip install -r requirements.txt --no-cache-dir
```

**问题**: dlib安装失败（Windows）
```bash
# 解决方案1：使用conda
conda install -c conda-forge dlib

# 解决方案2：安装预编译版本
pip install dlib-binary
```

### 2. 运行问题

**问题**: 内存不足
- 降低视频分辨率
- 减少批处理数量
- 关闭其他程序

**问题**: 处理速度慢
- 使用GPU加速
- 降低质量设置
- 优化输入文件

**问题**: 口型同步效果差
- 确保人脸清晰可见
- 使用高质量音频
- 调整质量参数

### 3. 输出问题

**问题**: 输出文件损坏
- 检查磁盘空间
- 验证输入文件
- 重新处理

**问题**: 音视频不同步
- 检查音频时长
- 调整音频参数
- 使用原始音频

## 高级功能

### 1. 自定义TTS

```python
# 使用自定义TTS引擎
from text_processor import TextProcessor

processor = TextProcessor()
# 配置自定义参数
processor.text_to_speech(text, output_path, method="gtts")
```

### 2. 音频后处理

```python
# 自定义音频处理
from audio_processor import AudioProcessor

processor = AudioProcessor()
# 应用自定义滤波器
processed_audio = processor.enhance_speech(audio, sample_rate)
```

### 3. 视频预处理

```python
# 自定义视频处理
from video_processor import VideoProcessor

processor = VideoProcessor()
# 调整视频参数
processor.resize_video(input_path, output_path, (512, 512))
```

## 最佳实践

### 1. 输入文件准备
- 使用稳定的摄像设备录制
- 确保光线充足
- 避免背景干扰
- 保持人脸居中

### 2. 文案编写
- 使用简洁明了的语言
- 避免过长的句子
- 添加适当的标点符号
- 考虑语音节奏

### 3. 质量控制
- 预览输入文件
- 测试小段视频
- 检查输出质量
- 必要时重新处理

### 4. 工作流程
1. 准备和验证输入文件
2. 选择合适的处理参数
3. 运行处理并监控进度
4. 检查输出质量
5. 必要时调整参数重新处理

## 技术支持

### 获取帮助
- 查看 `README.md` 详细文档
- 运行 `python test_system.py` 诊断问题
- 查看处理日志了解错误信息
- 在GitHub Issues中报告问题

### 社区资源
- 官方文档和教程
- 用户交流群
- 技术博客和视频教程
- 开源社区贡献

---

**提示**: 首次使用建议先运行示例程序熟悉系统功能，然后逐步尝试自己的内容。
