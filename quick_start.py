"""
AI数字人口型同步系统 - 快速启动脚本
"""
import os
import sys
import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext
import threading
from config import Config

class QuickStartGUI:
    """快速启动图形界面"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("AI数字人口型同步系统")
        self.root.geometry("800x600")
        
        # 变量
        self.video_path = tk.StringVar()
        self.audio_path = tk.StringVar()
        self.text_content = tk.StringVar()
        self.output_path = tk.StringVar()
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 标题
        title_label = tk.Label(self.root, text="AI数字人口型同步系统", 
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # 主框架
        main_frame = tk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # 输入视频
        video_frame = tk.Frame(main_frame)
        video_frame.pack(fill=tk.X, pady=5)
        tk.Label(video_frame, text="输入视频:", width=10, anchor="w").pack(side=tk.LEFT)
        tk.Entry(video_frame, textvariable=self.video_path, width=50).pack(side=tk.LEFT, padx=5)
        tk.Button(video_frame, text="浏览", command=self.browse_video).pack(side=tk.LEFT)
        
        # 输入音频（可选）
        audio_frame = tk.Frame(main_frame)
        audio_frame.pack(fill=tk.X, pady=5)
        tk.Label(audio_frame, text="输入音频:", width=10, anchor="w").pack(side=tk.LEFT)
        tk.Entry(audio_frame, textvariable=self.audio_path, width=50).pack(side=tk.LEFT, padx=5)
        tk.Button(audio_frame, text="浏览", command=self.browse_audio).pack(side=tk.LEFT)
        tk.Label(audio_frame, text="(可选)", fg="gray").pack(side=tk.LEFT, padx=5)
        
        # 文案内容
        text_frame = tk.Frame(main_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        tk.Label(text_frame, text="文案内容:", anchor="w").pack(anchor="w")
        self.text_area = scrolledtext.ScrolledText(text_frame, height=8, wrap=tk.WORD)
        self.text_area.pack(fill=tk.BOTH, expand=True, pady=5)
        self.text_area.insert("1.0", "请输入要生成语音的文案内容...")
        
        # 输出路径
        output_frame = tk.Frame(main_frame)
        output_frame.pack(fill=tk.X, pady=5)
        tk.Label(output_frame, text="输出路径:", width=10, anchor="w").pack(side=tk.LEFT)
        tk.Entry(output_frame, textvariable=self.output_path, width=50).pack(side=tk.LEFT, padx=5)
        tk.Button(output_frame, text="浏览", command=self.browse_output).pack(side=tk.LEFT)
        
        # 按钮框架
        button_frame = tk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=20)
        
        tk.Button(button_frame, text="开始处理", command=self.start_processing,
                 bg="green", fg="white", font=("Arial", 12, "bold")).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="检查依赖", command=self.check_dependencies).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="安装依赖", command=self.install_dependencies).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="退出", command=self.root.quit).pack(side=tk.RIGHT, padx=5)
        
        # 日志区域
        log_frame = tk.Frame(main_frame)
        log_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        tk.Label(log_frame, text="处理日志:", anchor="w").pack(anchor="w")
        self.log_area = scrolledtext.ScrolledText(log_frame, height=10, state=tk.DISABLED)
        self.log_area.pack(fill=tk.BOTH, expand=True)
        
    def browse_video(self):
        """浏览视频文件"""
        filename = filedialog.askopenfilename(
            title="选择视频文件",
            filetypes=[
                ("视频文件", "*.mp4 *.avi *.mov *.mkv *.wmv"),
                ("所有文件", "*.*")
            ]
        )
        if filename:
            self.video_path.set(filename)
    
    def browse_audio(self):
        """浏览音频文件"""
        filename = filedialog.askopenfilename(
            title="选择音频文件",
            filetypes=[
                ("音频文件", "*.wav *.mp3 *.aac *.flac"),
                ("所有文件", "*.*")
            ]
        )
        if filename:
            self.audio_path.set(filename)
    
    def browse_output(self):
        """浏览输出路径"""
        filename = filedialog.asksaveasfilename(
            title="保存输出视频",
            defaultextension=".mp4",
            filetypes=[
                ("MP4文件", "*.mp4"),
                ("所有文件", "*.*")
            ]
        )
        if filename:
            self.output_path.set(filename)
    
    def log_message(self, message):
        """添加日志消息"""
        self.log_area.config(state=tk.NORMAL)
        self.log_area.insert(tk.END, message + "\n")
        self.log_area.see(tk.END)
        self.log_area.config(state=tk.DISABLED)
        self.root.update()
    
    def check_dependencies(self):
        """检查依赖"""
        self.log_message("正在检查依赖...")
        
        try:
            from utils import check_dependencies
            if check_dependencies():
                self.log_message("✅ 所有依赖检查通过")
                messagebox.showinfo("成功", "所有依赖检查通过！")
            else:
                self.log_message("❌ 依赖检查失败")
                messagebox.showerror("错误", "依赖检查失败，请安装缺失的包")
        except Exception as e:
            self.log_message(f"❌ 依赖检查出错: {e}")
            messagebox.showerror("错误", f"依赖检查出错: {e}")
    
    def install_dependencies(self):
        """安装依赖"""
        self.log_message("正在启动依赖安装...")
        
        def install_thread():
            try:
                import subprocess
                result = subprocess.run([sys.executable, "install_dependencies.py"], 
                                      capture_output=True, text=True)
                
                if result.returncode == 0:
                    self.log_message("✅ 依赖安装完成")
                    messagebox.showinfo("成功", "依赖安装完成！")
                else:
                    self.log_message(f"❌ 依赖安装失败: {result.stderr}")
                    messagebox.showerror("错误", "依赖安装失败，请查看日志")
            except Exception as e:
                self.log_message(f"❌ 依赖安装出错: {e}")
                messagebox.showerror("错误", f"依赖安装出错: {e}")
        
        threading.Thread(target=install_thread, daemon=True).start()
    
    def validate_inputs(self):
        """验证输入"""
        video_file = self.video_path.get().strip()
        text_content = self.text_area.get("1.0", tk.END).strip()
        
        if not video_file:
            messagebox.showerror("错误", "请选择输入视频文件")
            return False
        
        if not os.path.exists(video_file):
            messagebox.showerror("错误", "视频文件不存在")
            return False
        
        if not text_content or text_content == "请输入要生成语音的文案内容...":
            messagebox.showerror("错误", "请输入文案内容")
            return False
        
        return True
    
    def start_processing(self):
        """开始处理"""
        if not self.validate_inputs():
            return
        
        # 获取输入
        video_file = self.video_path.get().strip()
        audio_file = self.audio_path.get().strip() if self.audio_path.get().strip() else None
        text_content = self.text_area.get("1.0", tk.END).strip()
        output_file = self.output_path.get().strip()
        
        # 生成输出文件名（如果未指定）
        if not output_file:
            from utils import create_output_filename
            base_name = os.path.splitext(os.path.basename(video_file))[0]
            output_filename = create_output_filename(base_name, "lip_sync")
            output_file = Config.get_output_file_path(output_filename)
            self.output_path.set(output_file)
        
        self.log_message("开始处理...")
        self.log_message(f"输入视频: {video_file}")
        if audio_file:
            self.log_message(f"输入音频: {audio_file}")
        self.log_message(f"文案内容: {text_content[:50]}...")
        self.log_message(f"输出路径: {output_file}")
        
        def processing_thread():
            try:
                from main import AIDigitalHuman
                
                # 初始化系统
                ai_human = AIDigitalHuman()
                
                # 处理
                if audio_file and os.path.exists(audio_file):
                    success = ai_human.process_with_text_and_audio(
                        video_file, audio_file, text_content, output_file
                    )
                else:
                    success = ai_human.process_with_text_only(
                        video_file, text_content, output_file
                    )
                
                if success:
                    self.log_message("✅ 处理完成！")
                    self.log_message(f"输出文件: {output_file}")
                    messagebox.showinfo("成功", f"处理完成！\n输出文件: {output_file}")
                else:
                    self.log_message("❌ 处理失败")
                    messagebox.showerror("错误", "处理失败，请查看日志")
                
                # 清理
                ai_human.cleanup()
                
            except Exception as e:
                self.log_message(f"❌ 处理出错: {e}")
                messagebox.showerror("错误", f"处理出错: {e}")
        
        # 在新线程中运行处理
        threading.Thread(target=processing_thread, daemon=True).start()
    
    def run(self):
        """运行GUI"""
        self.root.mainloop()

def main():
    """主函数"""
    # 创建必要的目录
    Config.create_directories()
    
    # 启动GUI
    app = QuickStartGUI()
    app.run()

if __name__ == "__main__":
    main()
