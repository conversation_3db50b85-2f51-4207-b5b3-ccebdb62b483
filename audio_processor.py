"""
音频处理模块
"""
import os
import numpy as np
try:
    import librosa
except ImportError:
    librosa = None
try:
    import soundfile as sf
except ImportError:
    sf = None
try:
    from scipy import signal
except ImportError:
    signal = None
import logging
from typing import Tuple, Optional
from config import Config
from utils import normalize_audio, save_audio, validate_audio_file

logger = logging.getLogger(__name__)

class AudioProcessor:
    """音频处理器"""
    
    def __init__(self):
        self.sample_rate = Config.AUDIO_SAMPLE_RATE
    
    def load_audio(self, audio_path: str, target_sr: Optional[int] = None) -> Tuple[np.ndarray, int]:
        """加载音频文件"""
        if not validate_audio_file(audio_path):
            raise ValueError(f"无效的音频文件: {audio_path}")
        
        try:
            target_sr = target_sr or self.sample_rate
            audio, sr = librosa.load(audio_path, sr=target_sr)
            
            logger.info(f"音频加载成功: {audio_path}")
            logger.info(f"采样率: {sr}, 时长: {len(audio)/sr:.2f}秒")
            
            return audio, sr
        except Exception as e:
            logger.error(f"音频加载失败: {e}")
            raise
    
    def resample_audio(self, audio: np.ndarray, orig_sr: int, target_sr: int) -> np.ndarray:
        """重采样音频"""
        if orig_sr == target_sr:
            return audio
        
        try:
            resampled_audio = librosa.resample(audio, orig_sr=orig_sr, target_sr=target_sr)
            logger.info(f"音频重采样: {orig_sr}Hz -> {target_sr}Hz")
            return resampled_audio
        except Exception as e:
            logger.error(f"音频重采样失败: {e}")
            raise
    
    def normalize_audio_levels(self, audio: np.ndarray, target_db: float = -20.0) -> np.ndarray:
        """音频电平归一化"""
        try:
            # 计算RMS
            rms = np.sqrt(np.mean(audio**2))
            if rms == 0:
                return audio
            
            # 计算当前dB
            current_db = 20 * np.log10(rms)
            
            # 计算增益
            gain_db = target_db - current_db
            gain_linear = 10**(gain_db / 20)
            
            # 应用增益
            normalized_audio = audio * gain_linear
            
            # 防止削波
            max_val = np.max(np.abs(normalized_audio))
            if max_val > 1.0:
                normalized_audio = normalized_audio / max_val
            
            logger.info(f"音频电平归一化完成，增益: {gain_db:.2f}dB")
            return normalized_audio
            
        except Exception as e:
            logger.error(f"音频电平归一化失败: {e}")
            return normalize_audio(audio)
    
    def remove_silence(self, audio: np.ndarray, sr: int, 
                      top_db: int = 30, frame_length: int = 2048, 
                      hop_length: int = 512) -> np.ndarray:
        """移除音频中的静音部分"""
        try:
            # 使用librosa的trim函数移除静音
            trimmed_audio, _ = librosa.effects.trim(
                audio, 
                top_db=top_db,
                frame_length=frame_length,
                hop_length=hop_length
            )
            
            logger.info(f"静音移除完成，原长度: {len(audio)/sr:.2f}s, 新长度: {len(trimmed_audio)/sr:.2f}s")
            return trimmed_audio
            
        except Exception as e:
            logger.error(f"静音移除失败: {e}")
            return audio
    
    def apply_noise_reduction(self, audio: np.ndarray, sr: int) -> np.ndarray:
        """简单的噪声抑制"""
        try:
            # 使用高通滤波器移除低频噪声
            nyquist = sr // 2
            low_cutoff = 80  # Hz
            high_cutoff = min(8000, nyquist - 1)  # Hz
            
            # 设计带通滤波器
            sos = signal.butter(5, [low_cutoff, high_cutoff], btype='band', 
                              fs=sr, output='sos')
            
            # 应用滤波器
            filtered_audio = signal.sosfilt(sos, audio)
            
            logger.info("噪声抑制完成")
            return filtered_audio
            
        except Exception as e:
            logger.error(f"噪声抑制失败: {e}")
            return audio
    
    def adjust_audio_duration(self, audio: np.ndarray, sr: int, 
                            target_duration: float) -> np.ndarray:
        """调整音频时长"""
        current_duration = len(audio) / sr
        
        if abs(current_duration - target_duration) < 0.1:  # 差异小于0.1秒则不调整
            return audio
        
        try:
            if current_duration < target_duration:
                # 需要延长音频
                padding_samples = int((target_duration - current_duration) * sr)
                # 在末尾添加静音
                padded_audio = np.pad(audio, (0, padding_samples), mode='constant')
                logger.info(f"音频延长: {current_duration:.2f}s -> {target_duration:.2f}s")
                return padded_audio
            else:
                # 需要缩短音频
                target_samples = int(target_duration * sr)
                truncated_audio = audio[:target_samples]
                logger.info(f"音频截断: {current_duration:.2f}s -> {target_duration:.2f}s")
                return truncated_audio
                
        except Exception as e:
            logger.error(f"音频时长调整失败: {e}")
            return audio
    
    def enhance_speech(self, audio: np.ndarray, sr: int) -> np.ndarray:
        """语音增强"""
        try:
            # 1. 移除静音
            enhanced_audio = self.remove_silence(audio, sr)
            
            # 2. 噪声抑制
            enhanced_audio = self.apply_noise_reduction(enhanced_audio, sr)
            
            # 3. 电平归一化
            enhanced_audio = self.normalize_audio_levels(enhanced_audio)
            
            logger.info("语音增强完成")
            return enhanced_audio
            
        except Exception as e:
            logger.error(f"语音增强失败: {e}")
            return normalize_audio(audio)
    
    def process_audio_for_lip_sync(self, input_path: str, output_path: str, 
                                 target_duration: Optional[float] = None) -> bool:
        """为口型同步处理音频"""
        try:
            # 加载音频
            audio, sr = self.load_audio(input_path)
            
            # 重采样到目标采样率
            if sr != self.sample_rate:
                audio = self.resample_audio(audio, sr, self.sample_rate)
                sr = self.sample_rate
            
            # 语音增强
            audio = self.enhance_speech(audio, sr)
            
            # 调整时长（如果指定）
            if target_duration:
                audio = self.adjust_audio_duration(audio, sr, target_duration)
            
            # 最终归一化
            audio = normalize_audio(audio)
            
            # 保存处理后的音频
            success = save_audio(audio, output_path, sr)
            
            if success:
                logger.info(f"音频处理完成: {output_path}")
            
            return success
            
        except Exception as e:
            logger.error(f"音频处理失败: {e}")
            return False
    
    def merge_audio_with_video_audio(self, speech_path: str, background_audio_path: str, 
                                   output_path: str, speech_volume: float = 1.0, 
                                   background_volume: float = 0.3) -> bool:
        """将语音与背景音频混合"""
        try:
            # 加载语音音频
            speech_audio, sr1 = self.load_audio(speech_path)
            
            # 加载背景音频
            bg_audio, sr2 = self.load_audio(background_audio_path)
            
            # 确保采样率一致
            if sr1 != sr2:
                bg_audio = self.resample_audio(bg_audio, sr2, sr1)
            
            # 调整长度
            min_length = min(len(speech_audio), len(bg_audio))
            speech_audio = speech_audio[:min_length]
            bg_audio = bg_audio[:min_length]
            
            # 混合音频
            mixed_audio = (speech_audio * speech_volume + 
                          bg_audio * background_volume)
            
            # 归一化防止削波
            mixed_audio = normalize_audio(mixed_audio)
            
            # 保存混合音频
            return save_audio(mixed_audio, output_path, sr1)
            
        except Exception as e:
            logger.error(f"音频混合失败: {e}")
            return False
