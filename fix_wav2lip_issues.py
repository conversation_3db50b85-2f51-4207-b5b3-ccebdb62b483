"""
修复Wav2Lip安装问题的脚本
"""
import os
import sys
import subprocess
import requests
from pathlib import Path

def fix_opencv_compatibility():
    """修复OpenCV兼容性问题"""
    print("=== 修复OpenCV兼容性问题 ===")
    
    wav2lip_requirements = "Wav2Lip/requirements.txt"
    
    if not os.path.exists(wav2lip_requirements):
        print("❌ Wav2Lip requirements.txt 不存在")
        return False
    
    # 读取原始requirements
    with open(wav2lip_requirements, 'r') as f:
        lines = f.readlines()
    
    # 修复版本兼容性
    fixed_lines = []
    for line in lines:
        line = line.strip()
        if line.startswith('opencv-python=='):
            # 使用兼容的OpenCV版本
            fixed_lines.append('opencv-python>=4.5.0')
            print("✅ 修复OpenCV版本要求")
        elif line.startswith('numpy=='):
            # 使用兼容的numpy版本
            fixed_lines.append('numpy>=1.19.0')
            print("✅ 修复numpy版本要求")
        elif line.startswith('torch=='):
            # 使用兼容的torch版本
            fixed_lines.append('torch>=1.8.0')
            print("✅ 修复torch版本要求")
        elif line and not line.startswith('#'):
            fixed_lines.append(line)
    
    # 写入修复后的requirements
    fixed_requirements = "Wav2Lip/requirements_fixed.txt"
    with open(fixed_requirements, 'w') as f:
        for line in fixed_lines:
            f.write(line + '\n')
    
    print(f"✅ 创建修复版本: {fixed_requirements}")
    return True

def download_models_alternative():
    """使用备用链接下载模型"""
    print("=== 使用备用链接下载模型 ===")
    
    os.makedirs('models', exist_ok=True)
    
    # 备用下载链接
    model_urls = {
        'wav2lip_gan.pth': [
            'https://huggingface.co/spaces/Rudrabha/Wav2Lip/resolve/main/checkpoints/wav2lip_gan.pth',
            'https://github.com/justinjohn0306/Wav2Lip/releases/download/models/wav2lip_gan.pth',
            'https://drive.google.com/uc?id=1NQFghz3KuJy4AFYcKjIMvMkjKmjdUzV9'  # Google Drive备用
        ],
        's3fd.pth': [
            'https://huggingface.co/spaces/Rudrabha/Wav2Lip/resolve/main/face_detection/detection/sfd/s3fd.pth',
            'https://github.com/justinjohn0306/Wav2Lip/releases/download/models/s3fd.pth'
        ]
    }
    
    success_count = 0
    
    for model_name, urls in model_urls.items():
        model_path = f'models/{model_name}'
        
        if os.path.exists(model_path):
            size = os.path.getsize(model_path) / (1024*1024)
            print(f"✅ {model_name} 已存在 ({size:.1f}MB)")
            success_count += 1
            continue
        
        print(f"正在下载 {model_name}...")
        
        downloaded = False
        for i, url in enumerate(urls):
            try:
                print(f"  尝试链接 {i+1}: {url[:50]}...")
                
                response = requests.get(url, stream=True, timeout=30)
                response.raise_for_status()
                
                total_size = int(response.headers.get('content-length', 0))
                downloaded_size = 0
                
                with open(model_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            downloaded_size += len(chunk)
                            if total_size > 0:
                                progress = (downloaded_size / total_size) * 100
                                print(f"\r  下载进度: {progress:.1f}%", end="", flush=True)
                
                print(f"\n✅ {model_name} 下载成功")
                downloaded = True
                success_count += 1
                break
                
            except Exception as e:
                print(f"\n  ❌ 链接 {i+1} 失败: {e}")
                continue
        
        if not downloaded:
            print(f"❌ {model_name} 所有链接都失败")
    
    return success_count == len(model_urls)

def install_fixed_dependencies():
    """安装修复后的依赖"""
    print("=== 安装修复后的依赖 ===")
    
    fixed_requirements = "Wav2Lip/requirements_fixed.txt"
    
    if not os.path.exists(fixed_requirements):
        print("❌ 修复版requirements不存在")
        return False
    
    try:
        # 先安装基础依赖
        basic_packages = [
            'torch>=1.8.0',
            'torchvision>=0.9.0',
            'opencv-python>=4.5.0',
            'numpy>=1.19.0',
            'pillow>=8.0.0',
            'librosa>=0.8.0',
            'scipy>=1.6.0',
            'tqdm>=4.60.0'
        ]
        
        for package in basic_packages:
            print(f"安装 {package}...")
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', package
            ], capture_output=True, text=True)
            
            if result.returncode != 0:
                print(f"⚠️ {package} 安装可能有问题，继续...")
        
        print("✅ 基础依赖安装完成")
        return True
        
    except Exception as e:
        print(f"❌ 依赖安装失败: {e}")
        return False

def create_simple_test():
    """创建简单测试脚本"""
    test_script = '''
import os
import sys

def test_wav2lip_setup():
    print("=== Wav2Lip安装测试 ===")
    
    # 检查文件
    files_to_check = [
        ("Wav2Lip/inference.py", "推理脚本"),
        ("models/wav2lip_gan.pth", "主模型文件"),
        ("Wav2Lip/models/wav2lip.py", "模型代码")
    ]
    
    all_good = True
    for file_path, description in files_to_check:
        if os.path.exists(file_path):
            if file_path.endswith('.pth'):
                size = os.path.getsize(file_path) / (1024*1024)
                print(f"✅ {description} ({size:.1f}MB)")
            else:
                print(f"✅ {description}")
        else:
            print(f"❌ {description} 缺失")
            all_good = False
    
    # 测试导入
    try:
        sys.path.insert(0, 'Wav2Lip')
        import models.wav2lip
        print("✅ Wav2Lip模块导入成功")
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        all_good = False
    
    if all_good:
        print("\\n🎉 Wav2Lip安装完成，可以使用完整功能！")
    else:
        print("\\n⚠️ Wav2Lip安装不完整，将使用简单口型同步")
    
    return all_good

if __name__ == "__main__":
    test_wav2lip_setup()
'''
    
    with open('test_wav2lip.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ 测试脚本已创建: test_wav2lip.py")

def main():
    """主函数"""
    print("Wav2Lip问题修复脚本")
    print("=" * 40)
    
    steps = [
        ("修复OpenCV兼容性", fix_opencv_compatibility),
        ("下载模型文件", download_models_alternative),
        ("安装修复依赖", install_fixed_dependencies),
        ("创建测试脚本", lambda: (create_simple_test(), True)[1])
    ]
    
    failed_steps = []
    
    for step_name, step_func in steps:
        print(f"\n--- {step_name} ---")
        try:
            if not step_func():
                failed_steps.append(step_name)
        except Exception as e:
            print(f"❌ {step_name} 出现异常: {e}")
            failed_steps.append(step_name)
    
    print("\n" + "=" * 40)
    print("修复总结:")
    
    if failed_steps:
        print(f"❌ 以下步骤失败: {', '.join(failed_steps)}")
    else:
        print("✅ 所有修复步骤完成")
    
    print("\n下一步操作:")
    print("1. 运行测试: python test_wav2lip.py")
    print("2. 测试主程序: python main.py --video ./assets/demo1_video.mp4 --text \"测试\" --output ./output/result.mp4")
    print("3. 如果仍有问题，系统会自动使用简单口型同步")
    
    return 0 if not failed_steps else 1

if __name__ == "__main__":
    sys.exit(main())
