# 快速修复指南 - Wav2Lip安装

## 问题描述
您遇到的错误是因为Wav2Lip模型没有正确安装。需要手动安装Wav2Lip和相关模型文件。

## 解决步骤

### 第1步：安装缺失的依赖
```bash
pip install requests
pip install gdown
```

### 第2步：手动下载和安装Wav2Lip

#### 方法1：使用Git（推荐）
```bash
# 在项目根目录执行
git clone https://github.com/Rudrabha/Wav2Lip.git
```

#### 方法2：手动下载（如果没有Git）
1. 访问：https://github.com/Rudrabha/Wav2Lip
2. 点击"Code" -> "Download ZIP"
3. 解压到项目根目录，重命名文件夹为 `Wav2Lip`

### 第3步：下载模型文件

创建 `models` 目录并下载模型：

```bash
# 创建模型目录
mkdir models

# 下载主要模型文件（约300MB）
# 方法1：使用wget（Linux/Mac）
wget https://github.com/Rudrabha/Wav2Lip/releases/download/Models/wav2lip_gan.pth -O models/wav2lip_gan.pth

# 方法2：使用PowerShell（Windows）
Invoke-WebRequest -Uri "https://github.com/Rudrabha/Wav2Lip/releases/download/Models/wav2lip_gan.pth" -OutFile "models/wav2lip_gan.pth"

# 方法3：使用Python脚本
python -c "
import requests
import os
os.makedirs('models', exist_ok=True)
url = 'https://github.com/Rudrabha/Wav2Lip/releases/download/Models/wav2lip_gan.pth'
print('正在下载模型文件...')
r = requests.get(url, stream=True)
with open('models/wav2lip_gan.pth', 'wb') as f:
    for chunk in r.iter_content(chunk_size=8192):
        if chunk:
            f.write(chunk)
print('模型下载完成！')
"
```

### 第4步：安装Wav2Lip依赖
```bash
cd Wav2Lip
pip install -r requirements.txt
cd ..
```

### 第5步：验证安装

运行以下命令检查安装：

```bash
python -c "
import os
print('检查Wav2Lip安装...')
if os.path.exists('Wav2Lip/inference.py'):
    print('✅ Wav2Lip代码已安装')
else:
    print('❌ Wav2Lip代码未找到')

if os.path.exists('models/wav2lip_gan.pth'):
    print('✅ 模型文件已下载')
    size = os.path.getsize('models/wav2lip_gan.pth') / (1024*1024)
    print(f'   模型文件大小: {size:.1f}MB')
else:
    print('❌ 模型文件未找到')
"
```

### 第6步：重新运行程序

```bash
python main.py --video ./assets/demo1_video.mp4 --text "你好，欢迎使用AI数字人系统！" --output ./output/result.mp4
```

## 一键安装脚本

如果上述步骤太复杂，可以运行以下一键安装脚本：

```bash
# 创建并运行一键安装脚本
python -c "
import os
import subprocess
import sys

def run_command(cmd, description):
    print(f'正在执行: {description}')
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        print(f'✅ {description} 完成')
        return True
    except subprocess.CalledProcessError as e:
        print(f'❌ {description} 失败: {e.stderr}')
        return False

# 安装依赖
run_command('pip install requests gdown', '安装依赖包')

# 克隆Wav2Lip
if not os.path.exists('Wav2Lip'):
    if not run_command('git clone https://github.com/Rudrabha/Wav2Lip.git', '克隆Wav2Lip仓库'):
        print('Git克隆失败，请手动下载')

# 创建模型目录
os.makedirs('models', exist_ok=True)

# 下载模型
if not os.path.exists('models/wav2lip_gan.pth'):
    print('正在下载模型文件（约300MB）...')
    try:
        import requests
        url = 'https://github.com/Rudrabha/Wav2Lip/releases/download/Models/wav2lip_gan.pth'
        r = requests.get(url, stream=True)
        total_size = int(r.headers.get('content-length', 0))
        downloaded = 0
        with open('models/wav2lip_gan.pth', 'wb') as f:
            for chunk in r.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        progress = (downloaded / total_size) * 100
                        print(f'\r下载进度: {progress:.1f}%', end='', flush=True)
        print('\n✅ 模型下载完成')
    except Exception as e:
        print(f'❌ 模型下载失败: {e}')

# 安装Wav2Lip依赖
if os.path.exists('Wav2Lip/requirements.txt'):
    run_command('pip install -r Wav2Lip/requirements.txt', '安装Wav2Lip依赖')

print('安装完成！现在可以重新运行主程序。')
"
```

## 常见问题

### Q: 模型下载很慢怎么办？
A: 可以使用国内镜像或手动下载：
1. 访问：https://github.com/Rudrabha/Wav2Lip/releases
2. 下载 `wav2lip_gan.pth` 文件
3. 放到 `models/` 目录下

### Q: Git克隆失败怎么办？
A: 手动下载ZIP文件：
1. 访问：https://github.com/Rudrabha/Wav2Lip
2. 下载ZIP并解压到项目目录
3. 重命名为 `Wav2Lip`

### Q: 依赖安装失败怎么办？
A: 尝试使用conda：
```bash
conda install pytorch torchvision torchaudio
conda install opencv
```

## 验证安装成功

安装完成后，您的项目目录应该包含：

```
AIRobbet/
├── Wav2Lip/
│   ├── inference.py
│   ├── models/
│   ├── face_detection/
│   └── ...
├── models/
│   └── wav2lip_gan.pth
├── main.py
└── ...
```

运行测试：
```bash
python -c "
import os
import sys
sys.path.append('Wav2Lip')
try:
    import models.wav2lip
    print('✅ Wav2Lip模块导入成功')
except ImportError as e:
    print(f'❌ 导入失败: {e}')
"
```

## 联系支持

如果仍有问题，请：
1. 检查网络连接
2. 确保有足够磁盘空间（至少1GB）
3. 查看详细错误信息
4. 在GitHub Issues中报告问题
