"""
工具函数模块
"""
import os
import cv2
import numpy as np
import librosa
import soundfile as sf
from typing import Tuple, Optional
import logging
from config import Config

# 设置日志
logging.basicConfig(level=logging.INFO if Config.DEBUG else logging.WARNING)
logger = logging.getLogger(__name__)

def setup_logging():
    """设置日志配置"""
    if Config.VERBOSE:
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

def validate_file_exists(file_path: str, file_type: str = "文件") -> bool:
    """验证文件是否存在"""
    if not os.path.exists(file_path):
        logger.error(f"{file_type}不存在: {file_path}")
        return False
    return True

def validate_video_file(video_path: str) -> bool:
    """验证视频文件是否有效"""
    if not validate_file_exists(video_path, "视频文件"):
        return False
    
    try:
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            logger.error(f"无法打开视频文件: {video_path}")
            return False
        
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        if frame_count == 0:
            logger.error(f"视频文件为空: {video_path}")
            return False
        
        cap.release()
        return True
    except Exception as e:
        logger.error(f"验证视频文件时出错: {e}")
        return False

def validate_audio_file(audio_path: str) -> bool:
    """验证音频文件是否有效"""
    if not validate_file_exists(audio_path, "音频文件"):
        return False
    
    try:
        audio, sr = librosa.load(audio_path, sr=None)
        if len(audio) == 0:
            logger.error(f"音频文件为空: {audio_path}")
            return False
        return True
    except Exception as e:
        logger.error(f"验证音频文件时出错: {e}")
        return False

def get_video_info(video_path: str) -> dict:
    """获取视频信息"""
    cap = cv2.VideoCapture(video_path)
    info = {
        'fps': cap.get(cv2.CAP_PROP_FPS),
        'frame_count': int(cap.get(cv2.CAP_PROP_FRAME_COUNT)),
        'width': int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
        'height': int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
        'duration': cap.get(cv2.CAP_PROP_FRAME_COUNT) / cap.get(cv2.CAP_PROP_FPS)
    }
    cap.release()
    return info

def get_audio_info(audio_path: str) -> dict:
    """获取音频信息"""
    audio, sr = librosa.load(audio_path, sr=None)
    info = {
        'sample_rate': sr,
        'duration': len(audio) / sr,
        'channels': 1 if audio.ndim == 1 else audio.shape[0]
    }
    return info

def resize_video_frame(frame: np.ndarray, target_size: Tuple[int, int]) -> np.ndarray:
    """调整视频帧大小"""
    return cv2.resize(frame, target_size)

def normalize_audio(audio: np.ndarray) -> np.ndarray:
    """音频归一化"""
    if audio.dtype != np.float32:
        audio = audio.astype(np.float32)
    
    # 归一化到 [-1, 1] 范围
    max_val = np.max(np.abs(audio))
    if max_val > 0:
        audio = audio / max_val
    
    return audio

def save_audio(audio: np.ndarray, output_path: str, sample_rate: int = Config.AUDIO_SAMPLE_RATE):
    """保存音频文件"""
    try:
        sf.write(output_path, audio, sample_rate)
        logger.info(f"音频已保存到: {output_path}")
        return True
    except Exception as e:
        logger.error(f"保存音频文件时出错: {e}")
        return False

def cleanup_temp_files():
    """清理临时文件"""
    if not Config.SAVE_INTERMEDIATE_FILES:
        temp_dir = Config.TEMP_DIR
        if os.path.exists(temp_dir):
            for file in os.listdir(temp_dir):
                file_path = os.path.join(temp_dir, file)
                try:
                    os.remove(file_path)
                    logger.info(f"已删除临时文件: {file_path}")
                except Exception as e:
                    logger.warning(f"删除临时文件失败: {e}")

def create_output_filename(base_name: str, suffix: str = "", extension: str = "mp4") -> str:
    """创建输出文件名"""
    timestamp = str(int(np.random.random() * 1000000))
    if suffix:
        filename = f"{base_name}_{suffix}_{timestamp}.{extension}"
    else:
        filename = f"{base_name}_{timestamp}.{extension}"
    return filename

def check_dependencies():
    """检查依赖项是否安装"""
    required_packages = ['cv2', 'numpy', 'librosa', 'torch']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"缺少必要的包: {missing_packages}")
        return False
    
    return True
