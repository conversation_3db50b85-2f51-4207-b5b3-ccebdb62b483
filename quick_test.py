"""
快速系统测试脚本
"""
import os
import sys

def check_system_status():
    """检查系统状态"""
    print("🔍 AI数字人系统状态检查")
    print("=" * 40)
    
    # 检查核心文件
    core_files = {
        'main.py': '主程序',
        'config.py': '配置文件',
        'utils.py': '工具模块',
        'text_processor.py': '文案处理',
        'audio_processor.py': '音频处理',
        'video_processor.py': '视频处理',
        'lip_sync_engine.py': '口型同步引擎',
        'simple_lip_sync.py': '简单口型同步',
        'quick_start.py': '图形界面'
    }
    
    print("\n📁 核心文件检查:")
    missing_files = []
    for file, desc in core_files.items():
        if os.path.exists(file):
            print(f"✅ {desc}: {file}")
        else:
            print(f"❌ {desc}: {file}")
            missing_files.append(file)
    
    # 检查目录
    print("\n📂 目录结构检查:")
    directories = ['temp', 'output', 'models', 'assets']
    for directory in directories:
        if os.path.exists(directory):
            print(f"✅ {directory}/ 目录存在")
        else:
            print(f"⚠️ {directory}/ 目录不存在（将自动创建）")
    
    # 检查Wav2Lip状态
    print("\n🤖 Wav2Lip状态检查:")
    if os.path.exists('Wav2Lip'):
        print("✅ Wav2Lip目录存在")
        if os.path.exists('Wav2Lip/inference.py'):
            print("✅ Wav2Lip推理脚本存在")
        else:
            print("❌ Wav2Lip推理脚本缺失")
    else:
        print("⚠️ Wav2Lip目录不存在")
    
    if os.path.exists('models/wav2lip_gan.pth'):
        size = os.path.getsize('models/wav2lip_gan.pth') / (1024*1024)
        print(f"✅ Wav2Lip模型存在 ({size:.1f}MB)")
    else:
        print("⚠️ Wav2Lip模型不存在")
    
    # 检查输入文件
    print("\n📹 输入文件检查:")
    test_video = './assets/demo1_video.mp4'
    if os.path.exists(test_video):
        size = os.path.getsize(test_video) / (1024*1024)
        print(f"✅ 测试视频存在: {test_video} ({size:.1f}MB)")
    else:
        print(f"❌ 测试视频不存在: {test_video}")
        print("   请准备一个包含人脸的视频文件")
    
    # 系统能力评估
    print("\n🎯 系统能力评估:")
    
    can_process_video = os.path.exists('video_processor.py')
    can_process_audio = os.path.exists('audio_processor.py')
    can_process_text = os.path.exists('text_processor.py')
    can_simple_lip_sync = os.path.exists('simple_lip_sync.py')
    can_full_lip_sync = (os.path.exists('Wav2Lip/inference.py') and 
                        os.path.exists('models/wav2lip_gan.pth'))
    
    print(f"{'✅' if can_process_video else '❌'} 视频处理")
    print(f"{'✅' if can_process_audio else '❌'} 音频处理")
    print(f"{'✅' if can_process_text else '❌'} 文案转语音")
    print(f"{'✅' if can_simple_lip_sync else '❌'} 简单口型同步")
    print(f"{'✅' if can_full_lip_sync else '⚠️'} 完整Wav2Lip口型同步")
    
    # 总结
    print("\n📋 系统状态总结:")
    
    if missing_files:
        print(f"❌ 系统不完整，缺少文件: {', '.join(missing_files)}")
        return False
    
    if can_process_video and can_process_audio and can_process_text:
        if can_full_lip_sync:
            print("🎉 系统完整，支持所有功能！")
            print("   推荐使用: python main.py --video 视频文件 --text \"文案\"")
        elif can_simple_lip_sync:
            print("✅ 系统基本完整，支持简单口型同步！")
            print("   推荐使用: python main.py --video 视频文件 --text \"文案\"")
            print("   系统会自动使用简单口型同步方法")
        else:
            print("⚠️ 系统部分可用，但口型同步功能受限")
    else:
        print("❌ 系统核心功能缺失")
        return False
    
    return True

def provide_usage_guide():
    """提供使用指南"""
    print("\n🚀 使用指南:")
    print("=" * 40)
    
    print("\n1️⃣ 命令行使用:")
    print("   python main.py --video 视频文件 --text \"文案内容\"")
    print("   示例: python main.py --video ./assets/demo1_video.mp4 --text \"你好！\"")
    
    print("\n2️⃣ 图形界面使用:")
    print("   python quick_start.py")
    
    print("\n3️⃣ 示例程序:")
    print("   python example.py")
    
    print("\n4️⃣ 系统测试:")
    print("   python test_system.py")
    
    print("\n💡 提示:")
    print("   - 如果Wav2Lip模型缺失，系统会自动使用简单口型同步")
    print("   - 添加 --verbose 参数可以看到详细处理过程")
    print("   - 输出文件默认保存在 output/ 目录")

def main():
    """主函数"""
    print("AI数字人口型同步系统 - 快速检查")
    
    # 检查系统状态
    system_ok = check_system_status()
    
    # 提供使用指南
    provide_usage_guide()
    
    # 最终建议
    print("\n🎯 下一步建议:")
    print("=" * 40)
    
    if system_ok:
        if os.path.exists('./assets/demo1_video.mp4'):
            print("✅ 系统就绪！可以立即测试:")
            print("   python main.py --video ./assets/demo1_video.mp4 --text \"你好，欢迎使用AI数字人系统！\" --output ./output/result.mp4")
        else:
            print("✅ 系统就绪！请准备视频文件后测试:")
            print("   1. 准备一个包含人脸的视频文件")
            print("   2. 运行: python main.py --video 你的视频.mp4 --text \"文案内容\"")
        
        print("\n🔧 如果想要最佳效果，可以安装完整Wav2Lip:")
        print("   1. 手动下载 wav2lip_gan.pth 模型文件")
        print("   2. 放到 models/ 目录下")
        print("   3. 重新运行程序")
    else:
        print("❌ 系统需要修复，请检查缺失的文件")
    
    return 0 if system_ok else 1

if __name__ == "__main__":
    sys.exit(main())
