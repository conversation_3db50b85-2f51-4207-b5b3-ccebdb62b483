"""
配置文件 - 包含所有系统配置参数
"""
import os

class Config:
    # 基础路径配置
    BASE_DIR = os.path.dirname(os.path.abspath(__file__))
    TEMP_DIR = os.path.join(BASE_DIR, "temp")
    OUTPUT_DIR = os.path.join(BASE_DIR, "output")
    MODELS_DIR = os.path.join(BASE_DIR, "models")
    
    # 音频配置
    AUDIO_SAMPLE_RATE = 16000
    AUDIO_FORMAT = "wav"
    TTS_LANGUAGE = "zh"  # 中文
    TTS_SLOW = False
    
    # 视频配置
    VIDEO_FPS = 25
    VIDEO_QUALITY = "high"  # high, medium, low
    VIDEO_FORMAT = "mp4"
    
    # 口型同步配置
    LIP_SYNC_MODEL = "wav2lip"  # wav2lip, sadtalker
    FACE_DETECTION_CONFIDENCE = 0.5
    LIP_SYNC_QUALITY = "high"
    
    # Wav2Lip 特定配置
    WAV2LIP_CHECKPOINT_PATH = os.path.join(MODELS_DIR, "wav2lip_gan.pth")
    WAV2LIP_FACE_DETECT_BATCH_SIZE = 16
    WAV2LIP_LIP_SYNC_BATCH_SIZE = 128
    
    # 文本处理配置
    MAX_TEXT_LENGTH = 1000
    TEXT_ENCODING = "utf-8"
    
    # 输出配置
    OUTPUT_VIDEO_CODEC = "libx264"
    OUTPUT_AUDIO_CODEC = "aac"
    OUTPUT_BITRATE = "1000k"
    
    # 调试配置
    DEBUG = True
    VERBOSE = True
    SAVE_INTERMEDIATE_FILES = True
    
    @classmethod
    def create_directories(cls):
        """创建必要的目录"""
        directories = [cls.TEMP_DIR, cls.OUTPUT_DIR, cls.MODELS_DIR]
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    @classmethod
    def get_temp_file_path(cls, filename):
        """获取临时文件路径"""
        return os.path.join(cls.TEMP_DIR, filename)
    
    @classmethod
    def get_output_file_path(cls, filename):
        """获取输出文件路径"""
        return os.path.join(cls.OUTPUT_DIR, filename)
