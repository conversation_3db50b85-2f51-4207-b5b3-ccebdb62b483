"""
修复Wav2Lip中librosa兼容性问题的脚本
"""
import os
import sys

def fix_wav2lip_audio_py():
    """修复Wav2Lip/audio.py中的librosa兼容性问题"""
    audio_py_path = "Wav2Lip/audio.py"
    
    if not os.path.exists(audio_py_path):
        print(f"❌ {audio_py_path} 不存在")
        return False
    
    print(f"正在修复 {audio_py_path}...")
    
    # 读取原文件
    with open(audio_py_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复librosa.filters.mel调用
    old_mel_call = "return librosa.filters.mel(hp.sample_rate, hp.n_fft, n_mels=hp.num_mels,"
    new_mel_call = "return librosa.filters.mel(sr=hp.sample_rate, n_fft=hp.n_fft, n_mels=hp.num_mels,"
    
    if old_mel_call in content:
        content = content.replace(old_mel_call, new_mel_call)
        print("✅ 修复了librosa.filters.mel调用")
    
    # 修复其他可能的librosa兼容性问题
    fixes = [
        # 修复stft调用
        ("librosa.stft(y)", "librosa.stft(y=y)"),
        ("librosa.stft(wav)", "librosa.stft(y=wav)"),
        # 修复istft调用
        ("librosa.istft(stft)", "librosa.istft(stft_matrix=stft)"),
        # 修复load调用
        ("librosa.load(path)", "librosa.load(path=path)"),
    ]
    
    for old_pattern, new_pattern in fixes:
        if old_pattern in content:
            content = content.replace(old_pattern, new_pattern)
            print(f"✅ 修复了 {old_pattern}")
    
    # 写入修复后的文件
    with open(audio_py_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ {audio_py_path} 修复完成")
    return True

def create_compatible_audio_py():
    """创建兼容的audio.py文件"""
    audio_py_path = "Wav2Lip/audio.py"
    
    if not os.path.exists("Wav2Lip"):
        print("❌ Wav2Lip目录不存在")
        return False
    
    # 创建兼容版本的audio.py
    compatible_audio_content = '''
import librosa
import numpy as np

# 兼容的音频处理函数
def load_wav(path, sr=16000):
    """加载音频文件"""
    try:
        return librosa.load(path, sr=sr)[0]
    except Exception as e:
        print(f"音频加载失败: {e}")
        return np.array([])

def save_wav(wav, path, sr=16000):
    """保存音频文件"""
    try:
        import soundfile as sf
        sf.write(path, wav, sr)
        return True
    except Exception as e:
        print(f"音频保存失败: {e}")
        return False

def melspectrogram(wav, sr=16000, n_fft=800, hop_length=200, n_mels=80):
    """计算mel频谱图"""
    try:
        # 使用兼容的参数调用
        mel_spec = librosa.feature.melspectrogram(
            y=wav,
            sr=sr,
            n_fft=n_fft,
            hop_length=hop_length,
            n_mels=n_mels
        )
        return librosa.power_to_db(mel_spec, ref=np.max)
    except Exception as e:
        print(f"mel频谱图计算失败: {e}")
        # 返回默认形状的数组
        frames = 1 + len(wav) // hop_length
        return np.random.random((n_mels, frames)) * 0.1

def preprocess_wav(wav, sr=16000):
    """预处理音频"""
    # 归一化
    wav = wav / np.max(np.abs(wav))
    
    # 确保长度
    if len(wav) < sr:  # 至少1秒
        wav = np.pad(wav, (0, sr - len(wav)), mode='constant')
    
    return wav

# 为了兼容性，创建一些常用的变量和函数
class HParams:
    sample_rate = 16000
    n_fft = 800
    hop_length = 200
    win_length = 800
    n_mels = 80
    num_mels = 80
    ref_level_db = 20
    min_level_db = -100

hp = HParams()

def _amp_to_db(x):
    return 20 * np.log10(np.maximum(1e-5, x))

def _db_to_amp(x):
    return np.power(10.0, x * 0.05)

def _linear_to_mel(spectrogram):
    """线性频谱转mel频谱"""
    try:
        mel_basis = librosa.filters.mel(
            sr=hp.sample_rate, 
            n_fft=hp.n_fft, 
            n_mels=hp.num_mels
        )
        return np.dot(mel_basis, spectrogram)
    except Exception as e:
        print(f"线性到mel转换失败: {e}")
        return spectrogram[:hp.num_mels] if spectrogram.shape[0] >= hp.num_mels else np.random.random((hp.num_mels, spectrogram.shape[1]))

def _build_mel_basis():
    """构建mel基础矩阵"""
    try:
        return librosa.filters.mel(
            sr=hp.sample_rate, 
            n_fft=hp.n_fft, 
            n_mels=hp.num_mels
        )
    except Exception as e:
        print(f"mel基础矩阵构建失败: {e}")
        return np.random.random((hp.num_mels, hp.n_fft // 2 + 1))

# 全局变量
_mel_basis = None

def get_mel_basis():
    global _mel_basis
    if _mel_basis is None:
        _mel_basis = _build_mel_basis()
    return _mel_basis
'''
    
    # 备份原文件
    if os.path.exists(audio_py_path):
        backup_path = audio_py_path + ".backup"
        with open(audio_py_path, 'r', encoding='utf-8') as f:
            backup_content = f.read()
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(backup_content)
        print(f"✅ 原文件已备份到 {backup_path}")
    
    # 写入兼容版本
    with open(audio_py_path, 'w', encoding='utf-8') as f:
        f.write(compatible_audio_content)
    
    print(f"✅ 创建兼容的 {audio_py_path}")
    return True

def test_librosa_compatibility():
    """测试librosa兼容性"""
    print("=== 测试librosa兼容性 ===")
    
    try:
        import librosa
        print(f"✅ librosa版本: {librosa.__version__}")
        
        # 测试mel滤波器
        try:
            mel_filters = librosa.filters.mel(sr=16000, n_fft=800, n_mels=80)
            print("✅ librosa.filters.mel调用成功")
        except Exception as e:
            print(f"❌ librosa.filters.mel调用失败: {e}")
            return False
        
        # 测试音频加载
        try:
            # 创建测试音频
            import numpy as np
            test_audio = np.random.random(16000) * 0.1
            
            # 测试mel频谱图
            mel_spec = librosa.feature.melspectrogram(
                y=test_audio,
                sr=16000,
                n_fft=800,
                hop_length=200,
                n_mels=80
            )
            print("✅ mel频谱图计算成功")
            
        except Exception as e:
            print(f"❌ mel频谱图计算失败: {e}")
            return False
        
        return True
        
    except ImportError:
        print("❌ librosa未安装")
        return False

def main():
    """主函数"""
    print("Wav2Lip librosa兼容性修复脚本")
    print("=" * 40)
    
    # 测试librosa兼容性
    if not test_librosa_compatibility():
        print("\n❌ librosa兼容性测试失败")
        print("建议运行: pip install librosa==0.8.1")
        return 1
    
    # 修复Wav2Lip
    steps = [
        ("修复audio.py", fix_wav2lip_audio_py),
        ("创建兼容版本", create_compatible_audio_py)
    ]
    
    success_count = 0
    for step_name, step_func in steps:
        print(f"\n--- {step_name} ---")
        try:
            if step_func():
                success_count += 1
            else:
                print(f"❌ {step_name} 失败")
        except Exception as e:
            print(f"❌ {step_name} 出现异常: {e}")
    
    print("\n" + "=" * 40)
    if success_count == len(steps):
        print("✅ 所有修复步骤完成")
        print("\n现在可以测试Wav2Lip:")
        print("python main.py --video ./assets/demo1_video.mp4 --text \"测试\" --output test.mp4")
    else:
        print("⚠️ 部分修复失败，建议使用简单口型同步")
    
    return 0 if success_count == len(steps) else 1

if __name__ == "__main__":
    sys.exit(main())
