
import os
import sys

def test_lip_sync_system():
    """测试口型同步系统"""
    print("=== 口型同步系统测试 ===")
    
    # 检查核心文件
    required_files = [
        "main.py",
        "simple_lip_sync.py",
        "lip_sync_engine.py"
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file}")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n❌ 缺少文件: {missing_files}")
        return False
    
    # 检查Wav2Lip状态
    print("\n--- Wav2Lip状态 ---")
    wav2lip_available = (
        os.path.exists("Wav2Lip/inference.py") and 
        os.path.exists("models/wav2lip_gan.pth")
    )
    
    if wav2lip_available:
        print("✅ Wav2Lip完整可用")
        print("   推荐使用完整Wav2Lip功能")
    else:
        print("⚠️ Wav2Lip不完整")
        print("   将使用简单口型同步")
    
    # 检查测试文件
    print("\n--- 测试文件检查 ---")
    test_video = "./assets/demo1_video.mp4"
    if os.path.exists(test_video):
        print(f"✅ 测试视频: {test_video}")
    else:
        print(f"❌ 测试视频不存在: {test_video}")
        print("   请准备一个包含人脸的视频文件")
        return False
    
    print("\n🎯 推荐测试命令:")
    if wav2lip_available:
        print("python main.py --video ./assets/demo1_video.mp4 --text \"测试Wav2Lip\" --output test_wav2lip.mp4")
    
    print("python main.py --video ./assets/demo1_video.mp4 --text \"测试简单口型同步\" --output test_simple.mp4")
    
    return True

if __name__ == "__main__":
    test_lip_sync_system()
