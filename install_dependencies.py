"""
依赖安装脚本
"""
import subprocess
import sys
import os
import platform

def run_command(command, description=""):
    """运行命令并处理错误"""
    print(f"正在执行: {description or command}")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ 成功: {description or command}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 失败: {description or command}")
        print(f"错误信息: {e.stderr}")
        return False

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ 需要Python 3.7或更高版本")
        return False
    
    print("✅ Python版本符合要求")
    return True

def install_basic_packages():
    """安装基础包"""
    packages = [
        "numpy",
        "opencv-python",
        "Pillow",
        "requests",
        "tqdm"
    ]
    
    print("\n=== 安装基础包 ===")
    success = True
    
    for package in packages:
        if not run_command(f"pip install {package}", f"安装 {package}"):
            success = False
    
    return success

def install_audio_packages():
    """安装音频处理包"""
    packages = [
        "librosa",
        "soundfile", 
        "scipy",
        "resampy",
        "numba"
    ]
    
    print("\n=== 安装音频处理包 ===")
    success = True
    
    for package in packages:
        if not run_command(f"pip install {package}", f"安装 {package}"):
            success = False
    
    return success

def install_video_packages():
    """安装视频处理包"""
    packages = [
        "moviepy",
        "imageio",
        "imageio-ffmpeg",
        "ffmpeg-python"
    ]
    
    print("\n=== 安装视频处理包 ===")
    success = True
    
    for package in packages:
        if not run_command(f"pip install {package}", f"安装 {package}"):
            success = False
    
    return success

def install_ml_packages():
    """安装机器学习包"""
    print("\n=== 安装机器学习包 ===")
    
    # 检查是否有CUDA
    try:
        import torch
        if torch.cuda.is_available():
            print("检测到CUDA，安装GPU版本的PyTorch")
            torch_command = "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118"
        else:
            print("未检测到CUDA，安装CPU版本的PyTorch")
            torch_command = "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu"
    except ImportError:
        print("PyTorch未安装，安装CPU版本")
        torch_command = "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu"
    
    return run_command(torch_command, "安装PyTorch")

def install_face_recognition():
    """安装人脸识别包"""
    print("\n=== 安装人脸识别包 ===")
    
    system = platform.system().lower()
    
    if system == "windows":
        print("Windows系统，需要先安装dlib...")
        # 在Windows上，dlib可能需要Visual Studio Build Tools
        print("如果dlib安装失败，请安装Visual Studio Build Tools")
        print("或者使用conda: conda install -c conda-forge dlib")
        
        success = run_command("pip install cmake", "安装cmake")
        if success:
            success = run_command("pip install dlib", "安装dlib")
        if success:
            success = run_command("pip install face-recognition", "安装face-recognition")
    else:
        # Linux/Mac
        success = run_command("pip install dlib", "安装dlib")
        if success:
            success = run_command("pip install face-recognition", "安装face-recognition")
    
    return success

def install_tts_packages():
    """安装文本转语音包"""
    packages = [
        "gTTS",
        "pyttsx3"
    ]
    
    print("\n=== 安装文本转语音包 ===")
    success = True
    
    for package in packages:
        if not run_command(f"pip install {package}", f"安装 {package}"):
            success = False
    
    return success

def check_installations():
    """检查安装结果"""
    print("\n=== 检查安装结果 ===")
    
    packages_to_check = [
        ("numpy", "import numpy"),
        ("opencv-python", "import cv2"),
        ("librosa", "import librosa"),
        ("torch", "import torch"),
        ("moviepy", "import moviepy"),
        ("face_recognition", "import face_recognition"),
        ("gTTS", "from gtts import gTTS"),
        ("pyttsx3", "import pyttsx3")
    ]
    
    success_count = 0
    total_count = len(packages_to_check)
    
    for package_name, import_statement in packages_to_check:
        try:
            exec(import_statement)
            print(f"✅ {package_name} - 安装成功")
            success_count += 1
        except ImportError:
            print(f"❌ {package_name} - 安装失败或不可用")
    
    print(f"\n安装结果: {success_count}/{total_count} 包可用")
    return success_count == total_count

def create_test_script():
    """创建测试脚本"""
    test_script = """
# 测试脚本
import sys

def test_imports():
    print("测试导入...")
    
    try:
        import numpy as np
        print("✅ numpy")
    except ImportError:
        print("❌ numpy")
    
    try:
        import cv2
        print("✅ opencv-python")
    except ImportError:
        print("❌ opencv-python")
    
    try:
        import torch
        print(f"✅ torch (CUDA可用: {torch.cuda.is_available()})")
    except ImportError:
        print("❌ torch")
    
    try:
        import librosa
        print("✅ librosa")
    except ImportError:
        print("❌ librosa")
    
    try:
        from gtts import gTTS
        print("✅ gTTS")
    except ImportError:
        print("❌ gTTS")
    
    try:
        import pyttsx3
        print("✅ pyttsx3")
    except ImportError:
        print("❌ pyttsx3")

if __name__ == "__main__":
    test_imports()
"""
    
    with open("test_dependencies.py", "w", encoding="utf-8") as f:
        f.write(test_script)
    
    print("✅ 测试脚本已创建: test_dependencies.py")

def main():
    """主函数"""
    print("AI数字人口型同步系统 - 依赖安装脚本")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        return 1
    
    # 升级pip
    print("\n=== 升级pip ===")
    run_command("python -m pip install --upgrade pip", "升级pip")
    
    # 安装各类包
    steps = [
        ("基础包", install_basic_packages),
        ("音频处理包", install_audio_packages),
        ("视频处理包", install_video_packages),
        ("机器学习包", install_ml_packages),
        ("人脸识别包", install_face_recognition),
        ("文本转语音包", install_tts_packages)
    ]
    
    failed_steps = []
    
    for step_name, step_func in steps:
        try:
            if not step_func():
                failed_steps.append(step_name)
        except Exception as e:
            print(f"❌ {step_name} 安装过程中出现异常: {e}")
            failed_steps.append(step_name)
    
    # 检查安装结果
    all_success = check_installations()
    
    # 创建测试脚本
    create_test_script()
    
    # 总结
    print("\n" + "=" * 50)
    print("安装完成总结:")
    
    if failed_steps:
        print(f"❌ 以下步骤安装失败: {', '.join(failed_steps)}")
        print("请手动安装失败的包或查看错误信息")
    
    if all_success:
        print("✅ 所有依赖包安装成功！")
        print("现在可以运行 python main.py 来使用系统")
    else:
        print("⚠️ 部分依赖包安装失败，系统可能无法正常工作")
        print("请运行 python test_dependencies.py 来检查具体问题")
    
    print("\n使用说明:")
    print("1. 运行 python test_dependencies.py 检查依赖")
    print("2. 运行 python example.py 查看使用示例")
    print("3. 运行 python main.py --help 查看命令行参数")
    
    return 0 if all_success else 1

if __name__ == "__main__":
    sys.exit(main())
