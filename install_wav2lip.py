"""
Wav2Lip 一键安装脚本
"""
import os
import subprocess
import sys

def run_command(cmd, description):
    """运行命令"""
    print(f'正在执行: {description}')
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        print(f'✅ {description} 完成')
        if result.stdout:
            print(f'输出: {result.stdout.strip()}')
        return True
    except subprocess.CalledProcessError as e:
        print(f'❌ {description} 失败')
        if e.stderr:
            print(f'错误: {e.stderr.strip()}')
        return False

def install_dependencies():
    """安装必要依赖"""
    print("=== 安装依赖包 ===")
    packages = ['requests', 'gdown', 'wget']
    
    for package in packages:
        try:
            __import__(package)
            print(f'✅ {package} 已安装')
        except ImportError:
            print(f'正在安装 {package}...')
            run_command(f'pip install {package}', f'安装 {package}')

def clone_wav2lip():
    """克隆Wav2Lip仓库"""
    print("\n=== 克隆Wav2Lip仓库 ===")
    
    if os.path.exists('Wav2Lip'):
        print('✅ Wav2Lip目录已存在')
        return True
    
    # 尝试使用git克隆
    if run_command('git --version', '检查Git'):
        if run_command('git clone https://github.com/Rudrabha/Wav2Lip.git', '克隆Wav2Lip仓库'):
            return True
    
    # Git失败，尝试下载ZIP
    print('Git克隆失败，尝试下载ZIP文件...')
    return download_wav2lip_zip()

def download_wav2lip_zip():
    """下载Wav2Lip ZIP文件"""
    try:
        import requests
        import zipfile
        
        print('正在下载Wav2Lip ZIP文件...')
        url = 'https://github.com/Rudrabha/Wav2Lip/archive/refs/heads/master.zip'
        
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        with open('Wav2Lip-master.zip', 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
        
        print('正在解压...')
        with zipfile.ZipFile('Wav2Lip-master.zip', 'r') as zip_ref:
            zip_ref.extractall('.')
        
        # 重命名目录
        if os.path.exists('Wav2Lip-master'):
            os.rename('Wav2Lip-master', 'Wav2Lip')
        
        # 删除ZIP文件
        os.remove('Wav2Lip-master.zip')
        
        print('✅ Wav2Lip下载解压完成')
        return True
        
    except Exception as e:
        print(f'❌ ZIP下载失败: {e}')
        return False

def download_models():
    """下载模型文件"""
    print("\n=== 下载模型文件 ===")
    
    os.makedirs('models', exist_ok=True)
    
    model_file = 'models/wav2lip_gan.pth'
    if os.path.exists(model_file):
        size = os.path.getsize(model_file) / (1024*1024)
        print(f'✅ 模型文件已存在 ({size:.1f}MB)')
        return True
    
    try:
        import requests
        
        print('正在下载模型文件（约300MB）...')
        url = 'https://github.com/Rudrabha/Wav2Lip/releases/download/Models/wav2lip_gan.pth'
        
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(model_file, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        progress = (downloaded / total_size) * 100
                        print(f'\r下载进度: {progress:.1f}%', end='', flush=True)
        
        print('\n✅ 模型下载完成')
        return True
        
    except Exception as e:
        print(f'\n❌ 模型下载失败: {e}')
        print('请手动下载模型文件：')
        print('1. 访问: https://github.com/Rudrabha/Wav2Lip/releases')
        print('2. 下载 wav2lip_gan.pth')
        print('3. 放到 models/ 目录下')
        return False

def install_wav2lip_deps():
    """安装Wav2Lip依赖"""
    print("\n=== 安装Wav2Lip依赖 ===")
    
    requirements_file = 'Wav2Lip/requirements.txt'
    if not os.path.exists(requirements_file):
        print('⚠️ requirements.txt 不存在，跳过')
        return True
    
    return run_command(f'pip install -r {requirements_file}', '安装Wav2Lip依赖')

def verify_installation():
    """验证安装"""
    print("\n=== 验证安装 ===")
    
    checks = [
        ('Wav2Lip/inference.py', 'Wav2Lip推理脚本'),
        ('models/wav2lip_gan.pth', '模型文件'),
        ('Wav2Lip/models/wav2lip.py', 'Wav2Lip模型代码'),
        ('Wav2Lip/face_detection/api.py', '人脸检测API')
    ]
    
    all_good = True
    for file_path, description in checks:
        if os.path.exists(file_path):
            if 'models/wav2lip_gan.pth' in file_path:
                size = os.path.getsize(file_path) / (1024*1024)
                print(f'✅ {description} ({size:.1f}MB)')
            else:
                print(f'✅ {description}')
        else:
            print(f'❌ {description} 缺失')
            all_good = False
    
    # 测试导入
    try:
        sys.path.insert(0, 'Wav2Lip')
        import models.wav2lip
        print('✅ Wav2Lip模块导入成功')
    except ImportError as e:
        print(f'❌ 模块导入失败: {e}')
        all_good = False
    
    return all_good

def main():
    """主函数"""
    print("Wav2Lip 一键安装脚本")
    print("=" * 40)
    
    steps = [
        ("安装依赖包", install_dependencies),
        ("克隆Wav2Lip仓库", clone_wav2lip),
        ("下载模型文件", download_models),
        ("安装Wav2Lip依赖", install_wav2lip_deps),
        ("验证安装", verify_installation)
    ]
    
    for step_name, step_func in steps:
        try:
            if not step_func():
                print(f"\n❌ {step_name} 失败")
                print("请查看上面的错误信息或参考 QUICK_FIX.md")
                return 1
        except Exception as e:
            print(f"\n❌ {step_name} 出现异常: {e}")
            return 1
    
    print("\n" + "=" * 40)
    print("🎉 Wav2Lip安装完成！")
    print("\n现在可以运行主程序:")
    print("python main.py --video ./assets/demo1_video.mp4 --text \"你好，欢迎使用AI数字人系统！\" --output ./output/result.mp4")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
