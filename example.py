"""
AI数字人口型同步系统 - 使用示例
"""
import os
import sys
from main import AIDigitalHuman
from config import Config
from utils import setup_logging

def example_basic_usage():
    """基本使用示例"""
    print("=== AI数字人口型同步系统 - 基本使用示例 ===")
    
    # 设置日志
    Config.VERBOSE = True
    setup_logging()
    
    # 初始化系统
    ai_human = AIDigitalHuman()
    
    # 示例文件路径（请替换为实际文件路径）
    video_path = "example_video.mp4"  # 输入视频
    text_content = "你好，我是AI数字人。欢迎使用口型同步系统！这个系统可以根据文案生成对应的口型动画。"
    output_path = Config.get_output_file_path("example_output.mp4")
    
    # 检查输入文件是否存在
    if not os.path.exists(video_path):
        print(f"错误：视频文件不存在 - {video_path}")
        print("请准备一个包含人脸的视频文件，并更新video_path变量")
        return False
    
    try:
        print(f"输入视频: {video_path}")
        print(f"文案内容: {text_content}")
        print(f"输出路径: {output_path}")
        print("\n开始处理...")
        
        # 处理视频和文案
        success = ai_human.process_with_text_only(video_path, text_content, output_path)
        
        if success:
            print(f"\n✅ 处理成功！")
            print(f"输出文件: {output_path}")
        else:
            print("\n❌ 处理失败")
            
        return success
        
    except Exception as e:
        print(f"\n❌ 处理过程中出现错误: {e}")
        return False
    finally:
        ai_human.cleanup()

def example_with_audio():
    """使用音频的示例"""
    print("=== AI数字人口型同步系统 - 音频+文案示例 ===")
    
    # 设置日志
    Config.VERBOSE = True
    setup_logging()
    
    # 初始化系统
    ai_human = AIDigitalHuman()
    
    # 示例文件路径
    video_path = "example_video.mp4"
    audio_path = "example_audio.wav"
    text_content = "这是一个带有音频的示例。系统会使用提供的音频文件进行口型同步。"
    output_path = Config.get_output_file_path("example_with_audio_output.mp4")
    
    # 检查输入文件
    if not os.path.exists(video_path):
        print(f"错误：视频文件不存在 - {video_path}")
        return False
    
    if not os.path.exists(audio_path):
        print(f"错误：音频文件不存在 - {audio_path}")
        return False
    
    try:
        print(f"输入视频: {video_path}")
        print(f"输入音频: {audio_path}")
        print(f"文案内容: {text_content}")
        print(f"输出路径: {output_path}")
        print("\n开始处理...")
        
        # 处理视频、音频和文案
        success = ai_human.process_with_text_and_audio(
            video_path, audio_path, text_content, output_path
        )
        
        if success:
            print(f"\n✅ 处理成功！")
            print(f"输出文件: {output_path}")
        else:
            print("\n❌ 处理失败")
            
        return success
        
    except Exception as e:
        print(f"\n❌ 处理过程中出现错误: {e}")
        return False
    finally:
        ai_human.cleanup()

def example_batch_processing():
    """批处理示例"""
    print("=== AI数字人口型同步系统 - 批处理示例 ===")
    
    # 设置日志
    Config.VERBOSE = True
    setup_logging()
    
    # 初始化系统
    ai_human = AIDigitalHuman()
    
    # 批处理任务列表
    tasks = [
        {
            "video": "video1.mp4",
            "text": "第一个视频的文案内容。",
            "output": "output1.mp4"
        },
        {
            "video": "video2.mp4", 
            "text": "第二个视频的文案内容。",
            "output": "output2.mp4"
        },
        {
            "video": "video3.mp4",
            "text": "第三个视频的文案内容。",
            "output": "output3.mp4"
        }
    ]
    
    success_count = 0
    total_count = len(tasks)
    
    try:
        for i, task in enumerate(tasks, 1):
            print(f"\n--- 处理任务 {i}/{total_count} ---")
            print(f"视频: {task['video']}")
            print(f"文案: {task['text']}")
            
            # 检查输入文件
            if not os.path.exists(task['video']):
                print(f"⚠️ 跳过：视频文件不存在 - {task['video']}")
                continue
            
            # 生成输出路径
            output_path = Config.get_output_file_path(task['output'])
            
            # 处理
            success = ai_human.process_with_text_only(
                task['video'], task['text'], output_path
            )
            
            if success:
                print(f"✅ 任务 {i} 完成: {output_path}")
                success_count += 1
            else:
                print(f"❌ 任务 {i} 失败")
        
        print(f"\n=== 批处理完成 ===")
        print(f"成功: {success_count}/{total_count}")
        
        return success_count == total_count
        
    except Exception as e:
        print(f"\n❌ 批处理过程中出现错误: {e}")
        return False
    finally:
        ai_human.cleanup()

def create_sample_files():
    """创建示例文件（仅用于演示）"""
    print("=== 创建示例文件 ===")
    
    # 创建示例文本文件
    sample_texts = [
        "你好，我是AI数字人助手。",
        "欢迎使用口型同步系统。",
        "这个系统可以让视频中的人物说出任何文案。",
        "技术改变生活，AI让未来更精彩。"
    ]
    
    sample_file_path = Config.get_output_file_path("sample_texts.txt")
    
    try:
        with open(sample_file_path, 'w', encoding='utf-8') as f:
            for i, text in enumerate(sample_texts, 1):
                f.write(f"示例文案 {i}: {text}\n")
        
        print(f"✅ 示例文本文件已创建: {sample_file_path}")
        
        # 显示使用说明
        print("\n📝 使用说明:")
        print("1. 准备一个包含清晰人脸的视频文件")
        print("2. 将视频文件路径更新到示例代码中")
        print("3. 运行相应的示例函数")
        print("4. 查看输出目录中的结果文件")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建示例文件失败: {e}")
        return False

def main():
    """主函数 - 运行示例"""
    print("AI数字人口型同步系统 - 示例程序")
    print("=" * 50)
    
    # 创建必要的目录
    Config.create_directories()
    
    # 显示菜单
    while True:
        print("\n请选择要运行的示例:")
        print("1. 基本使用示例（视频 + 文案）")
        print("2. 音频示例（视频 + 音频 + 文案）")
        print("3. 批处理示例")
        print("4. 创建示例文件")
        print("5. 退出")
        
        choice = input("\n请输入选择 (1-5): ").strip()
        
        if choice == '1':
            example_basic_usage()
        elif choice == '2':
            example_with_audio()
        elif choice == '3':
            example_batch_processing()
        elif choice == '4':
            create_sample_files()
        elif choice == '5':
            print("退出程序")
            break
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
