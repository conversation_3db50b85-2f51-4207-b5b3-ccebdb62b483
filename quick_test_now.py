"""
立即测试当前安装状态
"""
import sys

def test_current_status():
    """测试当前状态"""
    print("🔍 当前系统状态测试")
    print("=" * 30)
    
    print(f"Python版本: {sys.version}")
    
    # 测试已安装的包
    packages_to_test = [
        ("numpy", "import numpy as np; print(f'numpy {np.__version__}')"),
        ("opencv", "import cv2; print(f'opencv {cv2.__version__}')"),
        ("librosa", "import librosa; print(f'librosa {librosa.__version__}')"),
        ("soundfile", "import soundfile as sf; print('soundfile OK')"),
        ("scipy", "import scipy; print(f'scipy {scipy.__version__}')"),
        ("moviepy", "from moviepy.editor import VideoFileClip; print('moviepy OK')"),
        ("PIL", "from PIL import Image; print('PIL OK')"),
    ]
    
    available_packages = []
    
    for name, test_cmd in packages_to_test:
        try:
            exec(test_cmd)
            print(f"✅ {name}")
            available_packages.append(name)
        except ImportError as e:
            print(f"❌ {name} - 未安装")
        except Exception as e:
            print(f"⚠️ {name} - {e}")
    
    print(f"\n可用包: {len(available_packages)}/{len(packages_to_test)}")
    
    # 测试核心模块
    print("\n--- 核心模块测试 ---")
    core_modules = [
        "config",
        "utils", 
        "text_processor",
        "video_processor",
        "audio_processor",
        "simple_lip_sync"
    ]
    
    available_modules = []
    
    for module in core_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
            available_modules.append(module)
        except ImportError as e:
            print(f"❌ {module} - {e}")
        except Exception as e:
            print(f"⚠️ {module} - {e}")
    
    print(f"\n可用模块: {len(available_modules)}/{len(core_modules)}")
    
    # 判断系统状态
    if len(available_packages) >= 5 and len(available_modules) >= 4:
        print("\n🎉 系统基本可用！")
        print("\n可以尝试运行:")
        print("python main.py --video ./assets/demo1_video.mp4 --text \"测试\" --output test.mp4")
        return True
    elif len(available_modules) >= 3:
        print("\n✅ 核心功能可用，可以尝试运行")
        return True
    else:
        print("\n❌ 系统需要进一步修复")
        return False

def provide_immediate_solution():
    """提供立即可用的解决方案"""
    print("\n🚀 立即可用的解决方案:")
    print("=" * 30)
    
    print("1. 如果numpy/librosa可用:")
    print("   python main.py --video 视频文件 --text \"文案\" --output 输出文件")
    
    print("\n2. 如果仍有错误:")
    print("   # 跳过有问题的导入，使用基础功能")
    print("   # 系统会自动降级到可用的功能")
    
    print("\n3. 最小化测试:")
    print("   python -c \"from config import Config; print('配置OK')\"")
    print("   python -c \"import cv2; print('OpenCV OK')\"")
    
    print("\n4. 如果OpenCV可用但librosa有问题:")
    print("   # 系统会使用简化的音频处理")
    print("   # 仍然可以生成视频")

if __name__ == "__main__":
    if test_current_status():
        provide_immediate_solution()
    else:
        print("\n建议:")
        print("1. 等待torch安装完成")
        print("2. 或使用conda环境")
        print("3. 或尝试Python 3.8-3.10")
