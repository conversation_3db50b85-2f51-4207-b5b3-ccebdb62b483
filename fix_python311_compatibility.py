"""
Python 3.11兼容性修复脚本
"""
import subprocess
import sys
import os

def install_python311_compatible_packages():
    """安装Python 3.11兼容的包"""
    print("🔧 Python 3.11 兼容性修复")
    print("=" * 40)
    
    # Python 3.11兼容的版本
    packages = [
        "numpy>=1.23.0",
        "opencv-python>=4.8.0", 
        "librosa>=0.9.0",
        "soundfile>=0.12.0",
        "scipy>=1.9.0",
        "moviepy>=1.0.3",
        "pillow>=9.0.0",
        "torch>=1.13.0",
        "torchvision>=0.14.0",
        "torchaudio>=0.13.0",
        "gTTS>=2.3.0",
        "pyttsx3>=2.90"
    ]
    
    print("正在安装Python 3.11兼容的包...")
    
    success_count = 0
    for package in packages:
        try:
            print(f"正在安装 {package}...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", package, "--upgrade"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ {package}")
                success_count += 1
            else:
                print(f"⚠️ {package} - 可能已安装或有冲突")
                # 尝试不指定版本
                package_name = package.split(">=")[0].split("==")[0]
                result2 = subprocess.run([
                    sys.executable, "-m", "pip", "install", package_name, "--upgrade"
                ], capture_output=True, text=True)
                if result2.returncode == 0:
                    print(f"✅ {package_name} (最新版本)")
                    success_count += 1
        except Exception as e:
            print(f"❌ {package} - {e}")
    
    print(f"\n安装结果: {success_count}/{len(packages)} 成功")
    return success_count >= len(packages) * 0.7  # 70%成功率

def test_critical_imports():
    """测试关键导入"""
    print("\n🧪 测试关键导入...")
    
    critical_tests = [
        ("numpy", "import numpy as np; print(f'numpy {np.__version__}')"),
        ("opencv", "import cv2; print(f'opencv {cv2.__version__}')"),
        ("librosa", "import librosa; print(f'librosa {librosa.__version__}')"),
        ("torch", "import torch; print(f'torch {torch.__version__}')"),
        ("moviepy", "from moviepy.editor import VideoFileClip; print('moviepy OK')"),
    ]
    
    success_count = 0
    for name, test_cmd in critical_tests:
        try:
            result = subprocess.run([sys.executable, "-c", test_cmd], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ {name} - {result.stdout.strip()}")
                success_count += 1
            else:
                print(f"❌ {name} - {result.stderr.strip()}")
        except Exception as e:
            print(f"❌ {name} - {e}")
    
    return success_count >= len(critical_tests) * 0.8

def create_compatibility_test():
    """创建兼容性测试脚本"""
    test_content = '''
import sys
print(f"Python版本: {sys.version}")

def test_all_imports():
    """测试所有导入"""
    print("\\n=== 导入测试 ===")
    
    imports = [
        ("numpy", "import numpy as np"),
        ("opencv", "import cv2"),
        ("librosa", "import librosa"),
        ("soundfile", "import soundfile as sf"),
        ("scipy", "import scipy"),
        ("moviepy", "from moviepy.editor import VideoFileClip"),
        ("torch", "import torch"),
        ("PIL", "from PIL import Image"),
        ("gTTS", "from gtts import gTTS"),
    ]
    
    success = 0
    for name, cmd in imports:
        try:
            exec(cmd)
            print(f"✅ {name}")
            success += 1
        except ImportError as e:
            print(f"❌ {name} - {e}")
        except Exception as e:
            print(f"⚠️ {name} - {e}")
    
    print(f"\\n导入成功率: {success}/{len(imports)} ({success/len(imports)*100:.1f}%)")
    
    if success >= len(imports) * 0.8:
        print("\\n🎉 系统基本可用！")
        return True
    else:
        print("\\n⚠️ 系统可能有问题，但可以尝试运行")
        return False

def test_core_functionality():
    """测试核心功能"""
    print("\\n=== 核心功能测试 ===")
    
    try:
        # 测试配置
        from config import Config
        print("✅ 配置模块")
        
        # 测试工具函数
        from utils import validate_file_exists
        print("✅ 工具模块")
        
        # 测试处理器
        from text_processor import TextProcessor
        print("✅ 文案处理模块")
        
        from video_processor import VideoProcessor  
        print("✅ 视频处理模块")
        
        from audio_processor import AudioProcessor
        print("✅ 音频处理模块")
        
        from lip_sync_engine import LipSyncEngine
        print("✅ 口型同步模块")
        
        print("\\n🎉 所有核心模块可用！")
        return True
        
    except Exception as e:
        print(f"❌ 核心功能测试失败: {e}")
        return False

if __name__ == "__main__":
    print("AI数字人系统 - 兼容性测试")
    print("=" * 40)
    
    import_ok = test_all_imports()
    core_ok = test_core_functionality()
    
    if import_ok and core_ok:
        print("\\n🚀 系统完全可用！可以运行:")
        print("python main.py --video ./assets/demo1_video.mp4 --text \\"测试\\" --output test.mp4")
    elif core_ok:
        print("\\n✅ 核心功能可用，可以尝试运行")
    else:
        print("\\n❌ 系统需要进一步修复")
'''
    
    with open("compatibility_test.py", "w", encoding="utf-8") as f:
        f.write(test_content)
    
    print("✅ 兼容性测试脚本已创建: compatibility_test.py")

def main():
    """主函数"""
    print("AI数字人系统 - Python 3.11兼容性修复")
    print("=" * 50)
    
    print(f"当前Python版本: {sys.version}")
    
    # 安装兼容包
    if install_python311_compatible_packages():
        print("\n✅ 包安装基本完成")
        
        # 测试导入
        if test_critical_imports():
            print("\n🎉 关键导入测试通过！")
            
            # 创建测试脚本
            create_compatibility_test()
            
            print("\n🚀 下一步:")
            print("1. 运行兼容性测试: python compatibility_test.py")
            print("2. 测试主程序: python main.py --video ./assets/demo1_video.mp4 --text \"测试\" --output test.mp4")
            
            return 0
        else:
            print("\n⚠️ 部分导入失败，但可以尝试运行")
    else:
        print("\n❌ 包安装失败")
    
    print("\n🔄 如果仍有问题，建议:")
    print("1. 使用conda环境: conda create -n airobbet python=3.8")
    print("2. 或使用虚拟环境: python -m venv airobbet_env")
    print("3. 或降级到Python 3.8-3.10")
    
    return 1

if __name__ == "__main__":
    sys.exit(main())
