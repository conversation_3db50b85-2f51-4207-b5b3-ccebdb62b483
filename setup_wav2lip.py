"""
Wav2Lip 安装和配置脚本
"""
import os
import sys
import subprocess
import requests
import zipfile
from pathlib import Path
from config import Config

def download_file(url, local_path, description="文件"):
    """下载文件"""
    print(f"正在下载 {description}...")
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(local_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        progress = (downloaded / total_size) * 100
                        print(f"\r下载进度: {progress:.1f}%", end="", flush=True)
        
        print(f"\n✅ {description} 下载完成: {local_path}")
        return True
        
    except Exception as e:
        print(f"\n❌ {description} 下载失败: {e}")
        return False

def clone_wav2lip_repo():
    """克隆Wav2Lip仓库"""
    wav2lip_dir = os.path.join(Config.BASE_DIR, "Wav2Lip")
    
    if os.path.exists(wav2lip_dir):
        print(f"✅ Wav2Lip目录已存在: {wav2lip_dir}")
        return True
    
    print("正在克隆Wav2Lip仓库...")
    try:
        # 尝试使用git克隆
        result = subprocess.run([
            "git", "clone", "https://github.com/Rudrabha/Wav2Lip.git", wav2lip_dir
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ Wav2Lip仓库克隆成功: {wav2lip_dir}")
            return True
        else:
            print(f"❌ Git克隆失败: {result.stderr}")
            return download_wav2lip_zip()
            
    except FileNotFoundError:
        print("⚠️ Git未安装，尝试下载ZIP文件...")
        return download_wav2lip_zip()

def download_wav2lip_zip():
    """下载Wav2Lip ZIP文件"""
    wav2lip_dir = os.path.join(Config.BASE_DIR, "Wav2Lip")
    zip_path = os.path.join(Config.BASE_DIR, "Wav2Lip-main.zip")
    
    # 下载ZIP文件
    zip_url = "https://github.com/Rudrabha/Wav2Lip/archive/refs/heads/master.zip"
    if not download_file(zip_url, zip_path, "Wav2Lip ZIP文件"):
        return False
    
    # 解压ZIP文件
    try:
        print("正在解压Wav2Lip...")
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(Config.BASE_DIR)
        
        # 重命名解压后的目录
        extracted_dir = os.path.join(Config.BASE_DIR, "Wav2Lip-master")
        if os.path.exists(extracted_dir):
            os.rename(extracted_dir, wav2lip_dir)
        
        # 删除ZIP文件
        os.remove(zip_path)
        
        print(f"✅ Wav2Lip解压完成: {wav2lip_dir}")
        return True
        
    except Exception as e:
        print(f"❌ 解压失败: {e}")
        return False

def download_wav2lip_models():
    """下载Wav2Lip模型文件"""
    models_dir = Config.MODELS_DIR
    os.makedirs(models_dir, exist_ok=True)
    
    models_to_download = [
        {
            "name": "wav2lip_gan.pth",
            "url": "https://github.com/Rudrabha/Wav2Lip/releases/download/Models/wav2lip_gan.pth",
            "path": os.path.join(models_dir, "wav2lip_gan.pth")
        },
        {
            "name": "s3fd.pth", 
            "url": "https://github.com/Rudrabha/Wav2Lip/releases/download/Models/s3fd.pth",
            "path": os.path.join(models_dir, "s3fd.pth")
        }
    ]
    
    success_count = 0
    for model in models_to_download:
        if os.path.exists(model["path"]):
            print(f"✅ 模型已存在: {model['name']}")
            success_count += 1
        else:
            if download_file(model["url"], model["path"], f"模型 {model['name']}"):
                success_count += 1
    
    return success_count == len(models_to_download)

def install_wav2lip_dependencies():
    """安装Wav2Lip依赖"""
    wav2lip_dir = os.path.join(Config.BASE_DIR, "Wav2Lip")
    requirements_file = os.path.join(wav2lip_dir, "requirements.txt")
    
    if not os.path.exists(requirements_file):
        print("⚠️ Wav2Lip requirements.txt 不存在，跳过依赖安装")
        return True
    
    print("正在安装Wav2Lip依赖...")
    try:
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", requirements_file
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Wav2Lip依赖安装成功")
            return True
        else:
            print(f"❌ Wav2Lip依赖安装失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 安装依赖时出错: {e}")
        return False

def setup_wav2lip_paths():
    """设置Wav2Lip路径"""
    wav2lip_dir = os.path.join(Config.BASE_DIR, "Wav2Lip")
    
    # 检查关键文件
    key_files = [
        "inference.py",
        "models/wav2lip.py",
        "face_detection/api.py"
    ]
    
    missing_files = []
    for file_path in key_files:
        full_path = os.path.join(wav2lip_dir, file_path)
        if not os.path.exists(full_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺少关键文件: {missing_files}")
        return False
    
    # 添加到Python路径
    if wav2lip_dir not in sys.path:
        sys.path.insert(0, wav2lip_dir)
    
    print(f"✅ Wav2Lip路径设置完成: {wav2lip_dir}")
    return True

def create_wav2lip_wrapper():
    """创建Wav2Lip包装脚本"""
    wrapper_content = '''"""
Wav2Lip 包装脚本
"""
import os
import sys
import subprocess
from config import Config

def run_wav2lip(face_path, audio_path, output_path, checkpoint_path=None):
    """运行Wav2Lip推理"""
    wav2lip_dir = os.path.join(Config.BASE_DIR, "Wav2Lip")
    inference_script = os.path.join(wav2lip_dir, "inference.py")
    
    if not os.path.exists(inference_script):
        raise FileNotFoundError(f"Wav2Lip inference.py 不存在: {inference_script}")
    
    # 设置检查点路径
    if checkpoint_path is None:
        checkpoint_path = Config.WAV2LIP_CHECKPOINT_PATH
    
    # 构建命令
    cmd = [
        sys.executable, inference_script,
        "--checkpoint_path", checkpoint_path,
        "--face", face_path,
        "--audio", audio_path,
        "--outfile", output_path,
        "--static", "True",  # 静态模式，更稳定
        "--fps", str(Config.VIDEO_FPS)
    ]
    
    # 运行命令
    result = subprocess.run(cmd, cwd=wav2lip_dir, capture_output=True, text=True)
    
    if result.returncode == 0:
        return True, result.stdout
    else:
        return False, result.stderr

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser()
    parser.add_argument("--face", required=True)
    parser.add_argument("--audio", required=True) 
    parser.add_argument("--output", required=True)
    parser.add_argument("--checkpoint", default=None)
    
    args = parser.parse_args()
    
    success, message = run_wav2lip(args.face, args.audio, args.output, args.checkpoint)
    
    if success:
        print("✅ Wav2Lip处理成功")
        print(message)
    else:
        print("❌ Wav2Lip处理失败")
        print(message)
        sys.exit(1)
'''
    
    wrapper_path = os.path.join(Config.BASE_DIR, "wav2lip_wrapper.py")
    
    try:
        with open(wrapper_path, 'w', encoding='utf-8') as f:
            f.write(wrapper_content)
        
        print(f"✅ Wav2Lip包装脚本创建成功: {wrapper_path}")
        return True
        
    except Exception as e:
        print(f"❌ 创建包装脚本失败: {e}")
        return False

def test_wav2lip_installation():
    """测试Wav2Lip安装"""
    print("\n=== 测试Wav2Lip安装 ===")
    
    wav2lip_dir = os.path.join(Config.BASE_DIR, "Wav2Lip")
    inference_script = os.path.join(wav2lip_dir, "inference.py")
    
    # 检查文件存在
    if not os.path.exists(inference_script):
        print("❌ inference.py 不存在")
        return False
    
    # 检查模型文件
    model_path = Config.WAV2LIP_CHECKPOINT_PATH
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return False
    
    # 尝试导入模块
    try:
        sys.path.insert(0, wav2lip_dir)
        import models.wav2lip
        print("✅ Wav2Lip模块导入成功")
    except ImportError as e:
        print(f"❌ Wav2Lip模块导入失败: {e}")
        return False
    
    print("✅ Wav2Lip安装测试通过")
    return True

def main():
    """主函数"""
    print("Wav2Lip 安装和配置脚本")
    print("=" * 40)
    
    # 创建必要目录
    Config.create_directories()
    
    steps = [
        ("克隆Wav2Lip仓库", clone_wav2lip_repo),
        ("下载模型文件", download_wav2lip_models),
        ("安装依赖", install_wav2lip_dependencies),
        ("设置路径", setup_wav2lip_paths),
        ("创建包装脚本", create_wav2lip_wrapper),
        ("测试安装", test_wav2lip_installation)
    ]
    
    failed_steps = []
    
    for step_name, step_func in steps:
        print(f"\n--- {step_name} ---")
        try:
            if not step_func():
                failed_steps.append(step_name)
        except Exception as e:
            print(f"❌ {step_name} 执行失败: {e}")
            failed_steps.append(step_name)
    
    # 总结
    print("\n" + "=" * 40)
    print("安装总结:")
    
    if failed_steps:
        print(f"❌ 以下步骤失败: {', '.join(failed_steps)}")
        print("\n请检查网络连接和权限设置")
        return 1
    else:
        print("✅ Wav2Lip安装配置完成！")
        print("\n现在可以重新运行主程序:")
        print("python main.py --video ./assets/demo1_video.mp4 --text \"你好，欢迎使用AI数字人系统！\" --output ./output/result.mp4")
        return 0

if __name__ == "__main__":
    sys.exit(main())
