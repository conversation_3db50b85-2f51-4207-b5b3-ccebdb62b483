"""
Wav2Lip 包装脚本
"""
import os
import sys
import subprocess
from config import Config

def run_wav2lip(face_path, audio_path, output_path, checkpoint_path=None):
    """运行Wav2Lip推理"""
    wav2lip_dir = os.path.join(Config.BASE_DIR, "Wav2Lip")
    inference_script = os.path.join(wav2lip_dir, "inference.py")
    
    if not os.path.exists(inference_script):
        raise FileNotFoundError(f"Wav2Lip inference.py 不存在: {inference_script}")
    
    # 设置检查点路径
    if checkpoint_path is None:
        checkpoint_path = Config.WAV2LIP_CHECKPOINT_PATH
    
    # 构建命令
    cmd = [
        sys.executable, inference_script,
        "--checkpoint_path", checkpoint_path,
        "--face", face_path,
        "--audio", audio_path,
        "--outfile", output_path,
        "--static", "True",  # 静态模式，更稳定
        "--fps", str(Config.VIDEO_FPS)
    ]
    
    # 运行命令
    result = subprocess.run(cmd, cwd=wav2lip_dir, capture_output=True, text=True)
    
    if result.returncode == 0:
        return True, result.stdout
    else:
        return False, result.stderr

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser()
    parser.add_argument("--face", required=True)
    parser.add_argument("--audio", required=True) 
    parser.add_argument("--output", required=True)
    parser.add_argument("--checkpoint", default=None)
    
    args = parser.parse_args()
    
    success, message = run_wav2lip(args.face, args.audio, args.output, args.checkpoint)
    
    if success:
        print("✅ Wav2Lip处理成功")
        print(message)
    else:
        print("❌ Wav2Lip处理失败")
        print(message)
        sys.exit(1)
