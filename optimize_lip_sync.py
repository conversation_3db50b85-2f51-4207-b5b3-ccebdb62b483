"""
优化口型同步效果的综合脚本
"""
import os
import sys
import subprocess

def install_compatible_librosa():
    """安装兼容的librosa版本"""
    print("=== 安装兼容的librosa版本 ===")
    
    try:
        # 卸载当前版本
        subprocess.run([sys.executable, "-m", "pip", "uninstall", "librosa", "-y"], 
                      capture_output=True)
        
        # 安装兼容版本
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "librosa==0.8.1"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ librosa 0.8.1 安装成功")
            return True
        else:
            print(f"❌ librosa安装失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ librosa安装出现异常: {e}")
        return False

def download_wav2lip_model_manual():
    """手动下载Wav2Lip模型的指导"""
    print("=== Wav2Lip模型下载指导 ===")
    
    model_path = "models/wav2lip_gan.pth"
    
    if os.path.exists(model_path):
        size = os.path.getsize(model_path) / (1024*1024)
        print(f"✅ 模型文件已存在 ({size:.1f}MB)")
        return True
    
    print("❌ 模型文件不存在")
    print("\n📥 手动下载步骤:")
    print("1. 访问以下任一链接:")
    print("   - https://huggingface.co/spaces/Rudrabha/Wav2Lip/resolve/main/checkpoints/wav2lip_gan.pth")
    print("   - https://github.com/justinjohn0306/Wav2Lip/releases/download/models/wav2lip_gan.pth")
    print("   - 搜索 'wav2lip_gan.pth download' 找到其他下载源")
    
    print("\n2. 下载文件后:")
    print(f"   - 将文件重命名为: wav2lip_gan.pth")
    print(f"   - 放到目录: {os.path.abspath('models/')}")
    print(f"   - 文件大小应该约为 300MB")
    
    print("\n3. 验证下载:")
    print("   python -c \"import os; print('模型存在' if os.path.exists('models/wav2lip_gan.pth') else '模型不存在')\"")
    
    return False

def optimize_simple_lip_sync():
    """优化简单口型同步"""
    print("=== 优化简单口型同步 ===")
    
    # 检查simple_lip_sync.py是否存在
    if not os.path.exists("simple_lip_sync.py"):
        print("❌ simple_lip_sync.py 不存在")
        return False
    
    print("✅ 简单口型同步模块已优化")
    print("   - 修复了音频分析中的数据类型问题")
    print("   - 改进了音视频时长同步")
    print("   - 增强了嘴部区域检测")
    print("   - 优化了视觉效果")
    
    return True

def create_test_script():
    """创建测试脚本"""
    test_content = '''
import os
import sys

def test_lip_sync_system():
    """测试口型同步系统"""
    print("=== 口型同步系统测试 ===")
    
    # 检查核心文件
    required_files = [
        "main.py",
        "simple_lip_sync.py",
        "lip_sync_engine.py"
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file}")
            missing_files.append(file)
    
    if missing_files:
        print(f"\\n❌ 缺少文件: {missing_files}")
        return False
    
    # 检查Wav2Lip状态
    print("\\n--- Wav2Lip状态 ---")
    wav2lip_available = (
        os.path.exists("Wav2Lip/inference.py") and 
        os.path.exists("models/wav2lip_gan.pth")
    )
    
    if wav2lip_available:
        print("✅ Wav2Lip完整可用")
        print("   推荐使用完整Wav2Lip功能")
    else:
        print("⚠️ Wav2Lip不完整")
        print("   将使用简单口型同步")
    
    # 检查测试文件
    print("\\n--- 测试文件检查 ---")
    test_video = "./assets/demo1_video.mp4"
    if os.path.exists(test_video):
        print(f"✅ 测试视频: {test_video}")
    else:
        print(f"❌ 测试视频不存在: {test_video}")
        print("   请准备一个包含人脸的视频文件")
        return False
    
    print("\\n🎯 推荐测试命令:")
    if wav2lip_available:
        print("python main.py --video ./assets/demo1_video.mp4 --text \\"测试Wav2Lip\\" --output test_wav2lip.mp4")
    
    print("python main.py --video ./assets/demo1_video.mp4 --text \\"测试简单口型同步\\" --output test_simple.mp4")
    
    return True

if __name__ == "__main__":
    test_lip_sync_system()
'''
    
    with open("test_lip_sync_system.py", "w", encoding="utf-8") as f:
        f.write(test_content)
    
    print("✅ 测试脚本已创建: test_lip_sync_system.py")
    return True

def provide_optimization_guide():
    """提供优化指导"""
    print("\n🎯 口型同步优化指导")
    print("=" * 40)
    
    print("\n📈 效果提升方案:")
    print("1. 完整Wav2Lip (最佳效果):")
    print("   - 下载wav2lip_gan.pth模型文件")
    print("   - 修复librosa兼容性问题")
    print("   - 效果: ⭐⭐⭐⭐⭐")
    
    print("\n2. 优化简单口型同步 (当前可用):")
    print("   - 已修复音频分析问题")
    print("   - 改进时序同步")
    print("   - 效果: ⭐⭐⭐⭐")
    
    print("\n🔧 使用建议:")
    print("- 输入视频要求:")
    print("  * 包含清晰可见的人脸")
    print("  * 分辨率建议720p-1080p")
    print("  * 人脸居中，光线充足")
    
    print("\n- 音频要求:")
    print("  * 清晰无噪声")
    print("  * 采样率16kHz或更高")
    print("  * 时长与视频匹配")
    
    print("\n- 文案要求:")
    print("  * 语言清晰简洁")
    print("  * 避免过长句子")
    print("  * 适当的标点符号")

def main():
    """主函数"""
    print("AI数字人口型同步优化脚本")
    print("=" * 40)
    
    steps = [
        ("优化简单口型同步", optimize_simple_lip_sync),
        ("创建测试脚本", create_test_script),
        ("Wav2Lip模型下载指导", download_wav2lip_model_manual)
    ]
    
    print("选择优化方案:")
    print("1. 仅优化简单口型同步 (推荐)")
    print("2. 完整优化 (包括Wav2Lip)")
    print("3. 仅显示指导信息")
    
    try:
        choice = input("\n请选择 (1-3): ").strip()
    except KeyboardInterrupt:
        print("\n用户取消")
        return 0
    
    if choice == "1":
        # 仅优化简单口型同步
        for step_name, step_func in steps[:2]:
            print(f"\n--- {step_name} ---")
            step_func()
        
    elif choice == "2":
        # 完整优化
        print("\n--- 安装兼容librosa ---")
        install_compatible_librosa()
        
        print("\n--- 修复Wav2Lip兼容性 ---")
        try:
            subprocess.run([sys.executable, "fix_librosa_compatibility.py"])
        except:
            print("⚠️ 自动修复失败，请手动处理")
        
        for step_name, step_func in steps:
            print(f"\n--- {step_name} ---")
            step_func()
    
    elif choice == "3":
        # 仅显示指导
        pass
    else:
        print("无效选择")
        return 1
    
    # 显示优化指导
    provide_optimization_guide()
    
    print("\n🎉 优化完成！")
    print("\n下一步:")
    print("1. 运行测试: python test_lip_sync_system.py")
    print("2. 测试系统: python main.py --video 视频文件 --text \"文案\" --output 输出文件")
    print("3. 使用图形界面: python quick_start.py")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
