"""
AI数字人口型同步系统 - 系统测试脚本
"""
import os
import sys
import traceback
from config import Config

def test_imports():
    """测试模块导入"""
    print("=== 测试模块导入 ===")
    
    modules_to_test = [
        ("config", "from config import Config"),
        ("utils", "from utils import setup_logging, check_dependencies"),
        ("text_processor", "from text_processor import TextProcessor"),
        ("audio_processor", "from audio_processor import AudioProcessor"),
        ("video_processor", "from video_processor import VideoProcessor"),
        ("lip_sync_engine", "from lip_sync_engine import LipSyncEngine"),
        ("main", "from main import AIDigitalHuman")
    ]
    
    success_count = 0
    
    for module_name, import_statement in modules_to_test:
        try:
            exec(import_statement)
            print(f"✅ {module_name} - 导入成功")
            success_count += 1
        except Exception as e:
            print(f"❌ {module_name} - 导入失败: {e}")
    
    print(f"\n模块导入结果: {success_count}/{len(modules_to_test)}")
    return success_count == len(modules_to_test)

def test_dependencies():
    """测试依赖包"""
    print("\n=== 测试依赖包 ===")
    
    dependencies = [
        ("numpy", "import numpy as np"),
        ("opencv-python", "import cv2"),
        ("torch", "import torch"),
        ("librosa", "import librosa"),
        ("moviepy", "from moviepy.editor import VideoFileClip"),
        ("PIL", "from PIL import Image"),
        ("requests", "import requests"),
        ("tqdm", "from tqdm import tqdm")
    ]
    
    optional_dependencies = [
        ("face_recognition", "import face_recognition"),
        ("dlib", "import dlib"),
        ("gTTS", "from gtts import gTTS"),
        ("pyttsx3", "import pyttsx3"),
        ("soundfile", "import soundfile"),
        ("scipy", "from scipy import signal")
    ]
    
    success_count = 0
    total_count = len(dependencies)
    
    # 测试必需依赖
    print("必需依赖:")
    for dep_name, import_statement in dependencies:
        try:
            exec(import_statement)
            print(f"✅ {dep_name}")
            success_count += 1
        except ImportError:
            print(f"❌ {dep_name} - 未安装")
        except Exception as e:
            print(f"⚠️ {dep_name} - 导入错误: {e}")
    
    # 测试可选依赖
    print("\n可选依赖:")
    optional_success = 0
    for dep_name, import_statement in optional_dependencies:
        try:
            exec(import_statement)
            print(f"✅ {dep_name}")
            optional_success += 1
        except ImportError:
            print(f"⚠️ {dep_name} - 未安装（可选）")
        except Exception as e:
            print(f"⚠️ {dep_name} - 导入错误: {e}")
    
    print(f"\n依赖测试结果:")
    print(f"必需依赖: {success_count}/{total_count}")
    print(f"可选依赖: {optional_success}/{len(optional_dependencies)}")
    
    return success_count == total_count

def test_configuration():
    """测试配置"""
    print("\n=== 测试配置 ===")
    
    try:
        # 测试配置加载
        config = Config()
        print(f"✅ 配置加载成功")
        
        # 测试目录创建
        Config.create_directories()
        
        required_dirs = [Config.TEMP_DIR, Config.OUTPUT_DIR, Config.MODELS_DIR]
        for directory in required_dirs:
            if os.path.exists(directory):
                print(f"✅ 目录存在: {directory}")
            else:
                print(f"❌ 目录不存在: {directory}")
                return False
        
        # 测试配置参数
        print(f"✅ 音频采样率: {Config.AUDIO_SAMPLE_RATE}")
        print(f"✅ 视频FPS: {Config.VIDEO_FPS}")
        print(f"✅ 调试模式: {Config.DEBUG}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_processors():
    """测试处理器初始化"""
    print("\n=== 测试处理器初始化 ===")
    
    processors = [
        ("TextProcessor", "from text_processor import TextProcessor; TextProcessor()"),
        ("AudioProcessor", "from audio_processor import AudioProcessor; AudioProcessor()"),
        ("VideoProcessor", "from video_processor import VideoProcessor; VideoProcessor()"),
        ("LipSyncEngine", "from lip_sync_engine import LipSyncEngine; LipSyncEngine()")
    ]
    
    success_count = 0
    
    for processor_name, init_code in processors:
        try:
            exec(init_code)
            print(f"✅ {processor_name} - 初始化成功")
            success_count += 1
        except Exception as e:
            print(f"❌ {processor_name} - 初始化失败: {e}")
    
    print(f"\n处理器测试结果: {success_count}/{len(processors)}")
    return success_count == len(processors)

def test_main_system():
    """测试主系统"""
    print("\n=== 测试主系统 ===")
    
    try:
        from main import AIDigitalHuman
        
        # 初始化系统
        ai_human = AIDigitalHuman()
        print("✅ 主系统初始化成功")
        
        # 测试清理
        ai_human.cleanup()
        print("✅ 系统清理成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 主系统测试失败: {e}")
        traceback.print_exc()
        return False

def test_gpu_support():
    """测试GPU支持"""
    print("\n=== 测试GPU支持 ===")
    
    try:
        import torch
        
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            current_device = torch.cuda.current_device()
            device_name = torch.cuda.get_device_name(current_device)
            
            print(f"✅ CUDA可用")
            print(f"✅ GPU数量: {gpu_count}")
            print(f"✅ 当前设备: {device_name}")
            
            # 测试GPU内存
            memory_allocated = torch.cuda.memory_allocated() / 1024**2
            memory_cached = torch.cuda.memory_reserved() / 1024**2
            print(f"✅ GPU内存 - 已分配: {memory_allocated:.1f}MB, 已缓存: {memory_cached:.1f}MB")
            
            return True
        else:
            print("⚠️ CUDA不可用，将使用CPU")
            return True
            
    except Exception as e:
        print(f"❌ GPU测试失败: {e}")
        return False

def test_file_operations():
    """测试文件操作"""
    print("\n=== 测试文件操作 ===")
    
    try:
        from utils import create_output_filename
        
        # 测试文件名生成
        filename = create_output_filename("test", "suffix")
        print(f"✅ 文件名生成: {filename}")
        
        # 测试临时文件路径
        temp_path = Config.get_temp_file_path("test.txt")
        print(f"✅ 临时文件路径: {temp_path}")
        
        # 测试输出文件路径
        output_path = Config.get_output_file_path("test.mp4")
        print(f"✅ 输出文件路径: {output_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件操作测试失败: {e}")
        return False

def generate_test_report():
    """生成测试报告"""
    print("\n" + "="*50)
    print("AI数字人口型同步系统 - 测试报告")
    print("="*50)
    
    tests = [
        ("模块导入", test_imports),
        ("依赖包", test_dependencies),
        ("配置", test_configuration),
        ("处理器", test_processors),
        ("主系统", test_main_system),
        ("GPU支持", test_gpu_support),
        ("文件操作", test_file_operations)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n❌ {test_name}测试出现异常: {e}")
            results.append((test_name, False))
    
    # 生成总结
    print("\n" + "="*50)
    print("测试总结:")
    print("="*50)
    
    passed_count = 0
    total_count = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed_count += 1
    
    print(f"\n总体结果: {passed_count}/{total_count} 测试通过")
    
    if passed_count == total_count:
        print("🎉 所有测试通过！系统可以正常使用。")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关问题。")
        return False

def main():
    """主函数"""
    print("AI数字人口型同步系统 - 系统测试")
    
    # 创建必要目录
    Config.create_directories()
    
    # 运行测试
    success = generate_test_report()
    
    # 提供建议
    print("\n" + "="*50)
    print("使用建议:")
    print("="*50)
    
    if success:
        print("✅ 系统测试通过，可以开始使用：")
        print("   - 运行 python quick_start.py 启动图形界面")
        print("   - 运行 python main.py --help 查看命令行选项")
        print("   - 运行 python example.py 查看使用示例")
    else:
        print("⚠️ 系统测试未完全通过，建议：")
        print("   - 运行 python install_dependencies.py 安装缺失依赖")
        print("   - 检查Python版本是否为3.7+")
        print("   - 确保有足够的磁盘空间和内存")
    
    print("\n如有问题，请查看README.md或联系技术支持。")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
