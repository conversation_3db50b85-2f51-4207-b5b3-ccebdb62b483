
import sys
print(f"Python版本: {sys.version}")

def test_all_imports():
    """测试所有导入"""
    print("\n=== 导入测试 ===")
    
    imports = [
        ("numpy", "import numpy as np"),
        ("opencv", "import cv2"),
        ("librosa", "import librosa"),
        ("soundfile", "import soundfile as sf"),
        ("scipy", "import scipy"),
        ("moviepy", "from moviepy.editor import VideoFileClip"),
        ("torch", "import torch"),
        ("PIL", "from PIL import Image"),
        ("gTTS", "from gtts import gTTS"),
    ]
    
    success = 0
    for name, cmd in imports:
        try:
            exec(cmd)
            print(f"✅ {name}")
            success += 1
        except ImportError as e:
            print(f"❌ {name} - {e}")
        except Exception as e:
            print(f"⚠️ {name} - {e}")
    
    print(f"\n导入成功率: {success}/{len(imports)} ({success/len(imports)*100:.1f}%)")
    
    if success >= len(imports) * 0.8:
        print("\n🎉 系统基本可用！")
        return True
    else:
        print("\n⚠️ 系统可能有问题，但可以尝试运行")
        return False

def test_core_functionality():
    """测试核心功能"""
    print("\n=== 核心功能测试 ===")
    
    try:
        # 测试配置
        from config import Config
        print("✅ 配置模块")
        
        # 测试工具函数
        from utils import validate_file_exists
        print("✅ 工具模块")
        
        # 测试处理器
        from text_processor import TextProcessor
        print("✅ 文案处理模块")
        
        from video_processor import VideoProcessor  
        print("✅ 视频处理模块")
        
        from audio_processor import AudioProcessor
        print("✅ 音频处理模块")
        
        from lip_sync_engine import LipSyncEngine
        print("✅ 口型同步模块")
        
        print("\n🎉 所有核心模块可用！")
        return True
        
    except Exception as e:
        print(f"❌ 核心功能测试失败: {e}")
        return False

if __name__ == "__main__":
    print("AI数字人系统 - 兼容性测试")
    print("=" * 40)
    
    import_ok = test_all_imports()
    core_ok = test_core_functionality()
    
    if import_ok and core_ok:
        print("\n🚀 系统完全可用！可以运行:")
        print("python main.py --video ./assets/demo1_video.mp4 --text \"测试\" --output test.mp4")
    elif core_ok:
        print("\n✅ 核心功能可用，可以尝试运行")
    else:
        print("\n❌ 系统需要进一步修复")
