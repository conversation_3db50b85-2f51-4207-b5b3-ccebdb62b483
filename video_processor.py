"""
视频处理模块
"""
import os
import cv2
import numpy as np
try:
    import face_recognition
    FACE_RECOGNITION_AVAILABLE = True
except ImportError:
    FACE_RECOGNITION_AVAILABLE = False
    face_recognition = None

try:
    from moviepy.editor import VideoFileClip, AudioFileClip
    MOVIEPY_AVAILABLE = True
except ImportError:
    MOVIEPY_AVAILABLE = False
    VideoFileClip = None
    AudioFileClip = None

import logging
from typing import List, Tuple, Optional
from config import Config
from utils import validate_video_file, get_video_info, resize_video_frame

logger = logging.getLogger(__name__)

class VideoProcessor:
    """视频处理器"""
    
    def __init__(self):
        self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    
    def load_video(self, video_path: str) -> cv2.VideoCapture:
        """加载视频文件"""
        if not validate_video_file(video_path):
            raise ValueError(f"无效的视频文件: {video_path}")
        
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"无法打开视频文件: {video_path}")
        
        logger.info(f"视频加载成功: {video_path}")
        return cap
    
    def extract_frames(self, video_path: str, output_dir: str, 
                      frame_interval: int = 1) -> List[str]:
        """提取视频帧"""
        cap = self.load_video(video_path)
        frame_paths = []
        
        try:
            os.makedirs(output_dir, exist_ok=True)
            frame_count = 0
            saved_count = 0
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                if frame_count % frame_interval == 0:
                    frame_path = os.path.join(output_dir, f"frame_{saved_count:06d}.jpg")
                    cv2.imwrite(frame_path, frame)
                    frame_paths.append(frame_path)
                    saved_count += 1
                
                frame_count += 1
            
            logger.info(f"提取了 {saved_count} 帧到 {output_dir}")
            return frame_paths
            
        finally:
            cap.release()
    
    def detect_faces_in_frame(self, frame: np.ndarray) -> List[Tuple[int, int, int, int]]:
        """在帧中检测人脸"""
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # 使用OpenCV检测人脸
            faces = self.face_cascade.detectMultiScale(
                gray, 
                scaleFactor=1.1, 
                minNeighbors=5, 
                minSize=(30, 30)
            )
            
            # 转换为 (top, right, bottom, left) 格式
            face_locations = []
            for (x, y, w, h) in faces:
                face_locations.append((y, x + w, y + h, x))
            
            return face_locations
            
        except Exception as e:
            logger.error(f"人脸检测失败: {e}")
            return []
    
    def detect_faces_in_video(self, video_path: str, max_frames: int = 10) -> List[Tuple[int, int, int, int]]:
        """在视频中检测人脸"""
        cap = self.load_video(video_path)
        all_faces = []
        
        try:
            frame_count = 0
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            step = max(1, total_frames // max_frames)
            
            while frame_count < total_frames and len(all_faces) < max_frames:
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_count)
                ret, frame = cap.read()
                
                if not ret:
                    break
                
                faces = self.detect_faces_in_frame(frame)
                if faces:
                    all_faces.extend(faces)
                
                frame_count += step
            
            logger.info(f"在视频中检测到 {len(all_faces)} 个人脸")
            return all_faces
            
        finally:
            cap.release()
    
    def get_largest_face(self, faces: List[Tuple[int, int, int, int]]) -> Optional[Tuple[int, int, int, int]]:
        """获取最大的人脸"""
        if not faces:
            return None
        
        largest_face = max(faces, key=lambda face: (face[2] - face[0]) * (face[1] - face[3]))
        return largest_face
    
    def crop_face_region(self, frame: np.ndarray, face_location: Tuple[int, int, int, int], 
                        padding: float = 0.3) -> np.ndarray:
        """裁剪人脸区域"""
        top, right, bottom, left = face_location
        
        # 添加padding
        height, width = frame.shape[:2]
        face_width = right - left
        face_height = bottom - top
        
        pad_w = int(face_width * padding)
        pad_h = int(face_height * padding)
        
        # 计算裁剪区域
        crop_left = max(0, left - pad_w)
        crop_right = min(width, right + pad_w)
        crop_top = max(0, top - pad_h)
        crop_bottom = min(height, bottom + pad_h)
        
        # 裁剪
        cropped_face = frame[crop_top:crop_bottom, crop_left:crop_right]
        
        return cropped_face
    
    def resize_video(self, input_path: str, output_path: str, 
                    target_size: Tuple[int, int] = (512, 512)) -> bool:
        """调整视频尺寸"""
        try:
            cap = self.load_video(input_path)
            
            # 获取视频属性
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            # 创建视频写入器
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, target_size)
            
            frame_idx = 0
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # 调整帧大小
                resized_frame = resize_video_frame(frame, target_size)
                out.write(resized_frame)
                
                frame_idx += 1
                if frame_idx % 100 == 0:
                    logger.info(f"处理进度: {frame_idx}/{frame_count}")
            
            cap.release()
            out.release()
            
            logger.info(f"视频尺寸调整完成: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"视频尺寸调整失败: {e}")
            return False
    
    def extract_audio_from_video(self, video_path: str, audio_path: str) -> bool:
        """从视频中提取音频"""
        if MOVIEPY_AVAILABLE:
            try:
                video_clip = VideoFileClip(video_path)

                if video_clip.audio is None:
                    logger.warning("视频中没有音频轨道")
                    return False

                audio_clip = video_clip.audio
                audio_clip.write_audiofile(audio_path, verbose=False, logger=None)

                video_clip.close()
                audio_clip.close()

                logger.info(f"音频提取成功: {audio_path}")
                return True

            except Exception as e:
                logger.error(f"moviepy音频提取失败: {e}")
                return self._extract_audio_with_ffmpeg(video_path, audio_path)
        else:
            return self._extract_audio_with_ffmpeg(video_path, audio_path)

    def _extract_audio_with_ffmpeg(self, video_path: str, audio_path: str) -> bool:
        """使用ffmpeg提取音频"""
        try:
            import subprocess
            cmd = [
                'ffmpeg', '-y',
                '-i', video_path,
                '-vn',  # 不要视频
                '-acodec', 'pcm_s16le',  # 音频编码
                '-ar', '16000',  # 采样率
                '-ac', '1',  # 单声道
                audio_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                logger.info(f"ffmpeg音频提取成功: {audio_path}")
                return True
            else:
                logger.error(f"ffmpeg音频提取失败: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"ffmpeg音频提取失败: {e}")
            return False
    
    def combine_video_audio(self, video_path: str, audio_path: str,
                          output_path: str) -> bool:
        """合并视频和音频"""
        if MOVIEPY_AVAILABLE:
            try:
                video_clip = VideoFileClip(video_path)
                audio_clip = AudioFileClip(audio_path)

                # 调整音频长度以匹配视频
                if audio_clip.duration > video_clip.duration:
                    audio_clip = audio_clip.subclip(0, video_clip.duration)
                elif audio_clip.duration < video_clip.duration:
                    # 如果音频较短，可以选择循环或延长
                    video_clip = video_clip.subclip(0, audio_clip.duration)

                # 合并视频和音频
                final_clip = video_clip.set_audio(audio_clip)

                # 输出视频
                final_clip.write_videofile(
                    output_path,
                    codec=Config.OUTPUT_VIDEO_CODEC,
                    audio_codec=Config.OUTPUT_AUDIO_CODEC,
                    verbose=False,
                    logger=None
                )

                # 清理资源
                video_clip.close()
                audio_clip.close()
                final_clip.close()

                logger.info(f"视频音频合并完成: {output_path}")
                return True

            except Exception as e:
                logger.error(f"moviepy视频音频合并失败: {e}")
                return self._combine_video_audio_with_ffmpeg(video_path, audio_path, output_path)
        else:
            return self._combine_video_audio_with_ffmpeg(video_path, audio_path, output_path)

    def _combine_video_audio_with_ffmpeg(self, video_path: str, audio_path: str, output_path: str) -> bool:
        """使用ffmpeg合并视频和音频"""
        try:
            import subprocess
            cmd = [
                'ffmpeg', '-y',
                '-i', video_path,
                '-i', audio_path,
                '-c:v', 'copy',  # 复制视频流
                '-c:a', 'aac',   # 音频编码为AAC
                '-shortest',     # 以较短的流为准
                output_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                logger.info(f"ffmpeg视频音频合并完成: {output_path}")
                return True
            else:
                logger.error(f"ffmpeg合并失败: {result.stderr}")
                # 如果ffmpeg失败，使用OpenCV方法
                return self._combine_video_audio_opencv(video_path, audio_path, output_path)

        except Exception as e:
            logger.error(f"ffmpeg合并失败: {e}")
            return self._combine_video_audio_opencv(video_path, audio_path, output_path)

    def _combine_video_audio_opencv(self, video_path: str, audio_path: str, output_path: str) -> bool:
        """使用OpenCV和基础音频处理合并视频和音频"""
        try:
            logger.info("使用OpenCV方法合并视频和音频...")

            # 读取视频
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                logger.error(f"无法打开视频: {video_path}")
                return False

            fps = cap.get(cv2.CAP_PROP_FPS)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

            # 读取音频
            try:
                from scipy.io import wavfile
                sample_rate, audio_data = wavfile.read(audio_path)
                logger.info(f"音频加载成功: {sample_rate}Hz, {len(audio_data)}样本")
            except Exception as e:
                logger.error(f"读取音频失败: {e}")
                cap.release()
                return False

            # 计算时长并调整音频
            video_duration = frame_count / fps
            audio_duration = len(audio_data) / sample_rate

            logger.info(f"视频时长: {video_duration:.2f}s, 音频时长: {audio_duration:.2f}s")

            # 调整音频长度
            target_samples = int(video_duration * sample_rate)
            if len(audio_data) > target_samples:
                audio_data = audio_data[:target_samples]
            elif len(audio_data) < target_samples:
                padding = target_samples - len(audio_data)
                audio_data = np.pad(audio_data, (0, padding), mode='constant')

            # 创建输出视频
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

            # 复制所有帧并添加音频强度指示
            cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
            frame_idx = 0

            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                # 添加音频强度的视觉指示（绿色圆点）
                if frame_idx < len(audio_data):
                    audio_start = int(frame_idx * sample_rate / fps)
                    audio_end = int((frame_idx + 1) * sample_rate / fps)

                    if audio_end <= len(audio_data):
                        frame_audio = audio_data[audio_start:audio_end]
                        if len(frame_audio) > 0:
                            intensity = np.abs(frame_audio).mean()
                            indicator_size = int(intensity / 32767 * 15)  # 最大15像素
                            if indicator_size > 2:
                                cv2.circle(frame, (width-25, height-25), indicator_size, (0, 255, 0), -1)

                out.write(frame)
                frame_idx += 1

                if frame_idx % 50 == 0:
                    logger.info(f"合并进度: {frame_idx}/{frame_count}")

            cap.release()
            out.release()

            # 保存对应的音频文件
            audio_output_path = output_path.replace('.mp4', '_audio.wav')
            try:
                wavfile.write(audio_output_path, sample_rate, audio_data)
                logger.info(f"音频文件已保存: {audio_output_path}")
            except:
                pass

            logger.info(f"OpenCV视频音频合并完成: {output_path}")
            return True

        except Exception as e:
            logger.error(f"OpenCV合并失败: {e}")
            return self._simple_copy_video(video_path, output_path)

    def _simple_copy_video(self, video_path: str, output_path: str) -> bool:
        """简单复制视频文件"""
        try:
            import shutil
            shutil.copy2(video_path, output_path)
            logger.info(f"视频文件复制完成: {output_path}")
            return True
        except Exception as e:
            logger.error(f"视频文件复制失败: {e}")
            return False
    
    def preprocess_video_for_lip_sync(self, input_path: str, output_path: str) -> bool:
        """为口型同步预处理视频"""
        try:
            # 检测人脸
            faces = self.detect_faces_in_video(input_path)
            if not faces:
                logger.error("视频中未检测到人脸")
                return False
            
            # 获取最大人脸
            main_face = self.get_largest_face(faces)
            logger.info(f"主要人脸位置: {main_face}")
            
            # 调整视频尺寸（Wav2Lip通常需要特定尺寸）
            success = self.resize_video(input_path, output_path, (512, 512))
            
            return success
            
        except Exception as e:
            logger.error(f"视频预处理失败: {e}")
            return False
