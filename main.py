"""
AI数字人口型同步系统 - 主程序
"""
import os
import sys
import argparse
import logging
from typing import Optional
import time

# 导入自定义模块
from config import Config
from utils import setup_logging, check_dependencies, cleanup_temp_files, create_output_filename
from text_processor import TextProcessor
from audio_processor import AudioProcessor
from video_processor import VideoProcessor
from lip_sync_engine import LipSyncEngine

logger = logging.getLogger(__name__)

class AIDigitalHuman:
    """AI数字人口型同步系统主类"""
    
    def __init__(self):
        # 初始化各个处理器
        self.text_processor = TextProcessor()
        self.audio_processor = AudioProcessor()
        self.video_processor = VideoProcessor()
        self.lip_sync_engine = LipSyncEngine()
        
        # 创建必要的目录
        Config.create_directories()
        
        logger.info("AI数字人系统初始化完成")
    
    def process_text_to_speech(self, text: str, output_audio_path: str) -> bool:
        """将文案转换为语音"""
        logger.info("开始文案转语音处理...")
        
        try:
            success = self.text_processor.text_to_speech(text, output_audio_path)
            if success:
                logger.info(f"文案转语音完成: {output_audio_path}")
            return success
        except Exception as e:
            logger.error(f"文案转语音失败: {e}")
            return False
    
    def process_input_audio(self, input_audio_path: str, output_audio_path: str, 
                          target_duration: Optional[float] = None) -> bool:
        """处理输入的语音文件"""
        logger.info("开始处理输入语音...")
        
        try:
            success = self.audio_processor.process_audio_for_lip_sync(
                input_audio_path, output_audio_path, target_duration
            )
            if success:
                logger.info(f"语音处理完成: {output_audio_path}")
            return success
        except Exception as e:
            logger.error(f"语音处理失败: {e}")
            return False
    
    def process_input_video(self, input_video_path: str, output_video_path: str) -> bool:
        """处理输入的视频文件"""
        logger.info("开始处理输入视频...")
        
        try:
            success = self.video_processor.preprocess_video_for_lip_sync(
                input_video_path, output_video_path
            )
            if success:
                logger.info(f"视频处理完成: {output_video_path}")
            return success
        except Exception as e:
            logger.error(f"视频处理失败: {e}")
            return False
    
    def generate_lip_sync_video(self, video_path: str, audio_path: str, 
                              output_path: str) -> bool:
        """生成口型同步视频"""
        logger.info("开始生成口型同步视频...")
        
        try:
            success = self.lip_sync_engine.generate_lip_sync_video(
                video_path, audio_path, output_path
            )
            if success:
                logger.info(f"口型同步视频生成完成: {output_path}")
            return success
        except Exception as e:
            logger.error(f"口型同步视频生成失败: {e}")
            return False
    
    def combine_final_video(self, lip_sync_video_path: str, final_audio_path: str, 
                          output_path: str) -> bool:
        """合并最终视频和音频"""
        logger.info("开始合并最终视频...")
        
        try:
            success = self.video_processor.combine_video_audio(
                lip_sync_video_path, final_audio_path, output_path
            )
            if success:
                logger.info(f"最终视频生成完成: {output_path}")
            return success
        except Exception as e:
            logger.error(f"最终视频合并失败: {e}")
            return False
    
    def process_with_text_and_audio(self, video_path: str, audio_path: str, 
                                  text: str, output_path: str) -> bool:
        """使用视频、音频和文案生成口型同步视频"""
        logger.info("=== 开始处理：视频 + 音频 + 文案 ===")
        
        try:
            # 1. 处理输入视频
            processed_video_path = Config.get_temp_file_path("processed_video.mp4")
            if not self.process_input_video(video_path, processed_video_path):
                return False
            
            # 2. 处理输入音频
            processed_audio_path = Config.get_temp_file_path("processed_audio.wav")
            if not self.process_input_audio(audio_path, processed_audio_path):
                return False
            
            # 3. 文案转语音（作为参考或混合）
            tts_audio_path = Config.get_temp_file_path("tts_audio.wav")
            if not self.process_text_to_speech(text, tts_audio_path):
                logger.warning("文案转语音失败，将使用原始音频")
                final_audio_path = processed_audio_path
            else:
                # 可以选择混合音频或使用其中一个
                final_audio_path = processed_audio_path  # 这里使用原始音频
            
            # 4. 生成口型同步视频
            lip_sync_video_path = Config.get_temp_file_path("lip_sync_video.mp4")
            if not self.generate_lip_sync_video(processed_video_path, final_audio_path, lip_sync_video_path):
                return False
            
            # 5. 合并最终视频
            if not self.combine_final_video(lip_sync_video_path, final_audio_path, output_path):
                return False
            
            logger.info("=== 处理完成 ===")
            return True
            
        except Exception as e:
            logger.error(f"处理过程中出现错误: {e}")
            return False
    
    def process_with_text_only(self, video_path: str, text: str, output_path: str) -> bool:
        """仅使用视频和文案生成口型同步视频"""
        logger.info("=== 开始处理：视频 + 文案 ===")
        
        try:
            # 1. 处理输入视频
            processed_video_path = Config.get_temp_file_path("processed_video.mp4")
            if not self.process_input_video(video_path, processed_video_path):
                return False
            
            # 2. 文案转语音
            tts_audio_path = Config.get_temp_file_path("tts_audio.wav")
            if not self.process_text_to_speech(text, tts_audio_path):
                return False
            
            # 3. 生成口型同步视频
            lip_sync_video_path = Config.get_temp_file_path("lip_sync_video.mp4")
            if not self.generate_lip_sync_video(processed_video_path, tts_audio_path, lip_sync_video_path):
                return False
            
            # 4. 合并最终视频
            if not self.combine_final_video(lip_sync_video_path, tts_audio_path, output_path):
                return False
            
            logger.info("=== 处理完成 ===")
            return True
            
        except Exception as e:
            logger.error(f"处理过程中出现错误: {e}")
            return False
    
    def cleanup(self):
        """清理资源"""
        try:
            self.lip_sync_engine.cleanup()
            cleanup_temp_files()
            logger.info("资源清理完成")
        except Exception as e:
            logger.warning(f"资源清理时出现警告: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="AI数字人口型同步系统")
    parser.add_argument("--video", required=True, help="输入视频文件路径")
    parser.add_argument("--audio", help="输入音频文件路径（可选）")
    parser.add_argument("--text", required=True, help="文案内容")
    parser.add_argument("--output", help="输出视频文件路径（可选）")
    parser.add_argument("--verbose", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    # 设置日志
    if args.verbose:
        Config.VERBOSE = True
    setup_logging()
    
    # 检查依赖
    if not check_dependencies():
        logger.error("依赖检查失败，请安装必要的包")
        return 1
    
    # 验证输入文件
    if not os.path.exists(args.video):
        logger.error(f"视频文件不存在: {args.video}")
        return 1
    
    if args.audio and not os.path.exists(args.audio):
        logger.error(f"音频文件不存在: {args.audio}")
        return 1
    
    # 生成输出文件名
    if not args.output:
        base_name = os.path.splitext(os.path.basename(args.video))[0]
        output_filename = create_output_filename(base_name, "lip_sync")
        args.output = Config.get_output_file_path(output_filename)
    
    # 初始化系统
    ai_human = AIDigitalHuman()
    
    try:
        start_time = time.time()
        
        # 根据输入参数选择处理方式
        if args.audio:
            success = ai_human.process_with_text_and_audio(
                args.video, args.audio, args.text, args.output
            )
        else:
            success = ai_human.process_with_text_only(
                args.video, args.text, args.output
            )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        if success:
            logger.info(f"处理成功！输出文件: {args.output}")
            logger.info(f"处理时间: {processing_time:.2f}秒")
            return 0
        else:
            logger.error("处理失败")
            return 1
            
    except KeyboardInterrupt:
        logger.info("用户中断处理")
        return 1
    except Exception as e:
        logger.error(f"处理过程中出现未知错误: {e}")
        return 1
    finally:
        ai_human.cleanup()

if __name__ == "__main__":
    sys.exit(main())
