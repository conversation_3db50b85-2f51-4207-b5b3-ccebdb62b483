"""
文案处理模块 - 处理文本和语音合成
"""
import os
import re
import logging
from typing import Optional, List
import numpy as np
try:
    import pyttsx3
except ImportError:
    pyttsx3 = None
try:
    from gtts import gTTS
except ImportError:
    gTTS = None
import librosa
import soundfile as sf
from config import Config
from utils import save_audio, normalize_audio

logger = logging.getLogger(__name__)

class TextProcessor:
    """文案处理器"""
    
    def __init__(self):
        self.tts_engine = None
        self._init_tts_engine()
    
    def _init_tts_engine(self):
        """初始化TTS引擎"""
        if pyttsx3 is None:
            logger.warning("pyttsx3未安装，TTS引擎不可用")
            self.tts_engine = None
            return

        try:
            self.tts_engine = pyttsx3.init()
            # 设置语音参数
            voices = self.tts_engine.getProperty('voices')
            if voices:
                # 尝试设置中文语音
                for voice in voices:
                    if 'chinese' in voice.name.lower() or 'zh' in voice.id.lower():
                        self.tts_engine.setProperty('voice', voice.id)
                        break

            # 设置语速和音量
            self.tts_engine.setProperty('rate', 150)  # 语速
            self.tts_engine.setProperty('volume', 0.9)  # 音量

            logger.info("TTS引擎初始化成功")
        except Exception as e:
            logger.warning(f"TTS引擎初始化失败: {e}")
            self.tts_engine = None
    
    def preprocess_text(self, text: str) -> str:
        """预处理文本"""
        if not text:
            raise ValueError("文本不能为空")
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text.strip())
        
        # 检查文本长度
        if len(text) > Config.MAX_TEXT_LENGTH:
            logger.warning(f"文本长度超过限制 ({Config.MAX_TEXT_LENGTH})，将被截断")
            text = text[:Config.MAX_TEXT_LENGTH]
        
        # 处理标点符号，确保语音合成时有适当的停顿
        text = re.sub(r'[。！？]', '。', text)  # 统一句号
        text = re.sub(r'[，；]', '，', text)    # 统一逗号
        
        logger.info(f"文本预处理完成，长度: {len(text)}")
        return text
    
    def text_to_speech_pyttsx3(self, text: str, output_path: str) -> bool:
        """使用pyttsx3进行文本转语音"""
        if not self.tts_engine:
            logger.error("TTS引擎未初始化")
            return False
        
        try:
            self.tts_engine.save_to_file(text, output_path)
            self.tts_engine.runAndWait()
            
            if os.path.exists(output_path):
                logger.info(f"语音合成成功: {output_path}")
                return True
            else:
                logger.error("语音合成失败，文件未生成")
                return False
                
        except Exception as e:
            logger.error(f"pyttsx3语音合成失败: {e}")
            return False
    
    def text_to_speech_gtts(self, text: str, output_path: str) -> bool:
        """使用gTTS进行文本转语音"""
        if gTTS is None:
            logger.error("gTTS未安装，无法使用gTTS进行语音合成")
            return False

        try:
            tts = gTTS(
                text=text,
                lang=Config.TTS_LANGUAGE,
                slow=Config.TTS_SLOW
            )
            tts.save(output_path)

            if os.path.exists(output_path):
                logger.info(f"gTTS语音合成成功: {output_path}")
                return True
            else:
                logger.error("gTTS语音合成失败，文件未生成")
                return False

        except Exception as e:
            logger.error(f"gTTS语音合成失败: {e}")
            return False
    
    def process_audio_for_lip_sync(self, audio_path: str, output_path: str) -> bool:
        """处理音频以适配口型同步"""
        try:
            # 加载音频
            audio, sr = librosa.load(audio_path, sr=Config.AUDIO_SAMPLE_RATE)
            
            # 音频归一化
            audio = normalize_audio(audio)
            
            # 确保音频长度至少1秒
            min_length = Config.AUDIO_SAMPLE_RATE  # 1秒
            if len(audio) < min_length:
                # 用静音填充
                padding = min_length - len(audio)
                audio = np.pad(audio, (0, padding), mode='constant')
            
            # 保存处理后的音频
            return save_audio(audio, output_path, Config.AUDIO_SAMPLE_RATE)
            
        except Exception as e:
            logger.error(f"音频处理失败: {e}")
            return False
    
    def text_to_speech(self, text: str, output_path: str, method: str = "gtts") -> bool:
        """文本转语音主函数"""
        # 预处理文本
        processed_text = self.preprocess_text(text)
        
        # 创建临时文件路径
        temp_audio_path = Config.get_temp_file_path("temp_tts.wav")
        
        # 选择TTS方法
        success = False
        if method == "gtts":
            success = self.text_to_speech_gtts(processed_text, temp_audio_path)
        elif method == "pyttsx3":
            success = self.text_to_speech_pyttsx3(processed_text, temp_audio_path)
        else:
            logger.error(f"不支持的TTS方法: {method}")
            return False
        
        if not success:
            # 尝试备用方法
            backup_method = "pyttsx3" if method == "gtts" else "gtts"
            logger.info(f"尝试备用TTS方法: {backup_method}")
            if backup_method == "gtts":
                success = self.text_to_speech_gtts(processed_text, temp_audio_path)
            else:
                success = self.text_to_speech_pyttsx3(processed_text, temp_audio_path)
        
        if success:
            # 处理音频以适配口型同步
            return self.process_audio_for_lip_sync(temp_audio_path, output_path)
        
        return False
    
    def split_long_text(self, text: str, max_length: int = 100) -> List[str]:
        """将长文本分割成短句"""
        sentences = re.split(r'[。！？]', text)
        result = []
        current_chunk = ""
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
                
            if len(current_chunk + sentence) <= max_length:
                current_chunk += sentence + "。"
            else:
                if current_chunk:
                    result.append(current_chunk.strip())
                current_chunk = sentence + "。"
        
        if current_chunk:
            result.append(current_chunk.strip())
        
        return result
